package gorm_migration

import (
	"fmt"
	"log"
	"strings"
	"sync"
	"time"
)

// SQLValidator SQL验证器，用于对比GORM v1和v2生成的SQL
type SQLValidator struct {
	v1SQLs []SQLRecord
	v2SQLs []SQLRecord
	mutex  sync.RWMutex
}

type SQLRecord struct {
	SQL       string
	Args      []interface{}
	Timestamp time.Time
	Method    string // 调用的方法名
	Version   string // v1 或 v2
}

var GlobalSQLValidator = &SQLValidator{
	v1SQLs: make([]SQLRecord, 0),
	v2SQLs: make([]SQLRecord, 0),
}

// CaptureSQL 捕获SQL语句
func (sv *SQLValidator) CaptureSQL(version, method string, sql string, args []interface{}) {
	sv.mutex.Lock()
	defer sv.mutex.Unlock()
	
	record := SQLRecord{
		SQL:       sql,
		Args:      args,
		Timestamp: time.Now(),
		Method:    method,
		Version:   version,
	}
	
	if version == "v1" {
		sv.v1SQLs = append(sv.v1SQLs, record)
	} else {
		sv.v2SQLs = append(sv.v2SQLs, record)
	}
}

// CompareSQL 对比SQL差异
func (sv *SQLValidator) CompareSQL() []SQLDifference {
	sv.mutex.RLock()
	defer sv.mutex.RUnlock()
	
	differences := make([]SQLDifference, 0)
	
	// 按方法名分组对比
	v1ByMethod := sv.groupByMethod(sv.v1SQLs)
	v2ByMethod := sv.groupByMethod(sv.v2SQLs)
	
	for method, v1Records := range v1ByMethod {
		v2Records, exists := v2ByMethod[method]
		if !exists {
			differences = append(differences, SQLDifference{
				Method:   method,
				Type:     "MISSING_V2",
				V1SQL:    v1Records[0].SQL,
				V2SQL:    "",
				Severity: "HIGH",
			})
			continue
		}
		
		// 对比SQL语句
		if !sv.isSQLEquivalent(v1Records[0].SQL, v2Records[0].SQL) {
			differences = append(differences, SQLDifference{
				Method:   method,
				Type:     "SQL_DIFFERENT",
				V1SQL:    v1Records[0].SQL,
				V2SQL:    v2Records[0].SQL,
				Severity: sv.assessSeverity(v1Records[0].SQL, v2Records[0].SQL),
			})
		}
	}
	
	return differences
}

type SQLDifference struct {
	Method   string
	Type     string // MISSING_V2, SQL_DIFFERENT, ARGS_DIFFERENT
	V1SQL    string
	V2SQL    string
	Severity string // LOW, MEDIUM, HIGH
}

func (sv *SQLValidator) groupByMethod(records []SQLRecord) map[string][]SQLRecord {
	grouped := make(map[string][]SQLRecord)
	for _, record := range records {
		grouped[record.Method] = append(grouped[record.Method], record)
	}
	return grouped
}

func (sv *SQLValidator) isSQLEquivalent(sql1, sql2 string) bool {
	// 标准化SQL进行对比
	normalized1 := sv.normalizeSQL(sql1)
	normalized2 := sv.normalizeSQL(sql2)
	return normalized1 == normalized2
}

func (sv *SQLValidator) normalizeSQL(sql string) string {
	// 移除多余空格，统一大小写等
	sql = strings.TrimSpace(sql)
	sql = strings.ToUpper(sql)
	// 移除多余的空格
	words := strings.Fields(sql)
	return strings.Join(words, " ")
}

func (sv *SQLValidator) assessSeverity(sql1, sql2 string) string {
	// 评估SQL差异的严重程度
	if strings.Contains(sql1, "UPDATE") || strings.Contains(sql1, "DELETE") {
		return "HIGH"
	}
	if strings.Contains(sql1, "INSERT") {
		return "MEDIUM"
	}
	return "LOW"
}

// PrintSQLComparison 打印SQL对比结果
func (sv *SQLValidator) PrintSQLComparison() {
	differences := sv.CompareSQL()
	
	if len(differences) == 0 {
		log.Println("✅ SQL对比通过：GORM v1和v2生成的SQL一致")
		return
	}
	
	log.Printf("⚠️  发现 %d 个SQL差异：\n", len(differences))
	for _, diff := range differences {
		fmt.Printf("\n🔍 方法: %s (严重程度: %s)\n", diff.Method, diff.Severity)
		fmt.Printf("   类型: %s\n", diff.Type)
		fmt.Printf("   GORM v1: %s\n", diff.V1SQL)
		fmt.Printf("   GORM v2: %s\n", diff.V2SQL)
		fmt.Println("   " + strings.Repeat("-", 50))
	}
}

// Clear 清空记录
func (sv *SQLValidator) Clear() {
	sv.mutex.Lock()
	defer sv.mutex.Unlock()
	sv.v1SQLs = sv.v1SQLs[:0]
	sv.v2SQLs = sv.v2SQLs[:0]
}

// StartSQLCapture 开始SQL捕获
func StartSQLCapture() {
	GlobalSQLValidator.Clear()
	log.Println("🎯 开始SQL捕获...")
}

// StopSQLCapture 停止SQL捕获并输出对比结果
func StopSQLCapture() {
	log.Println("🛑 停止SQL捕获，开始对比...")
	GlobalSQLValidator.PrintSQLComparison()
}
