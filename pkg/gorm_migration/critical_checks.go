package gorm_migration

import (
	"fmt"
	"log"
	"strings"
)

// CriticalDifferenceChecker 关键差异检查器
type CriticalDifferenceChecker struct {
	rules []DifferenceRule
}

type DifferenceRule struct {
	Name        string
	Description string
	CheckFunc   func(v1SQL, v2SQL string) (bool, string) // 返回是否有问题，问题描述
}

// NewCriticalDifferenceChecker 创建关键差异检查器
func NewCriticalDifferenceChecker() *CriticalDifferenceChecker {
	checker := &CriticalDifferenceChecker{
		rules: make([]DifferenceRule, 0),
	}
	
	// 添加默认检查规则
	checker.addDefaultRules()
	return checker
}

func (cdc *CriticalDifferenceChecker) addDefaultRules() {
	// 规则1：检查UPDATE语句的WHERE条件
	cdc.rules = append(cdc.rules, DifferenceRule{
		Name:        "UPDATE_WHERE_CONDITION",
		Description: "检查UPDATE语句的WHERE条件是否一致",
		CheckFunc: func(v1SQL, v2SQL string) (bool, string) {
			if !strings.Contains(v1SQL, "UPDATE") {
				return false, ""
			}
			
			v1Where := extractWhereClause(v1SQL)
			v2Where := extractWhereClause(v2SQL)
			
			if v1Where != v2Where {
				return true, fmt.Sprintf("WHERE条件不一致: v1[%s] vs v2[%s]", v1Where, v2Where)
			}
			return false, ""
		},
	})
	
	// 规则2：检查DELETE语句的安全性
	cdc.rules = append(cdc.rules, DifferenceRule{
		Name:        "DELETE_SAFETY",
		Description: "检查DELETE语句是否有WHERE条件",
		CheckFunc: func(v1SQL, v2SQL string) (bool, string) {
			if !strings.Contains(v1SQL, "DELETE") {
				return false, ""
			}
			
			if !strings.Contains(v1SQL, "WHERE") || !strings.Contains(v2SQL, "WHERE") {
				return true, "DELETE语句缺少WHERE条件，存在安全风险"
			}
			return false, ""
		},
	})
	
	// 规则3：检查JOIN语句的差异
	cdc.rules = append(cdc.rules, DifferenceRule{
		Name:        "JOIN_DIFFERENCE",
		Description: "检查JOIN语句的差异",
		CheckFunc: func(v1SQL, v2SQL string) (bool, string) {
			v1Joins := extractJoins(v1SQL)
			v2Joins := extractJoins(v2SQL)
			
			if len(v1Joins) != len(v2Joins) {
				return true, fmt.Sprintf("JOIN数量不一致: v1[%d] vs v2[%d]", len(v1Joins), len(v2Joins))
			}
			
			for i, v1Join := range v1Joins {
				if i < len(v2Joins) && v1Join != v2Joins[i] {
					return true, fmt.Sprintf("JOIN条件不一致: v1[%s] vs v2[%s]", v1Join, v2Joins[i])
				}
			}
			return false, ""
		},
	})
	
	// 规则4：检查ORDER BY差异
	cdc.rules = append(cdc.rules, DifferenceRule{
		Name:        "ORDER_BY_DIFFERENCE",
		Description: "检查ORDER BY子句的差异",
		CheckFunc: func(v1SQL, v2SQL string) (bool, string) {
			v1OrderBy := extractOrderBy(v1SQL)
			v2OrderBy := extractOrderBy(v2SQL)
			
			if v1OrderBy != v2OrderBy {
				return true, fmt.Sprintf("ORDER BY不一致: v1[%s] vs v2[%s]", v1OrderBy, v2OrderBy)
			}
			return false, ""
		},
	})
	
	// 规则5：检查LIMIT差异
	cdc.rules = append(cdc.rules, DifferenceRule{
		Name:        "LIMIT_DIFFERENCE", 
		Description: "检查LIMIT子句的差异",
		CheckFunc: func(v1SQL, v2SQL string) (bool, string) {
			v1Limit := extractLimit(v1SQL)
			v2Limit := extractLimit(v2SQL)
			
			if v1Limit != v2Limit {
				return true, fmt.Sprintf("LIMIT不一致: v1[%s] vs v2[%s]", v1Limit, v2Limit)
			}
			return false, ""
		},
	})
	
	// 规则6：检查字段选择差异
	cdc.rules = append(cdc.rules, DifferenceRule{
		Name:        "SELECT_FIELDS_DIFFERENCE",
		Description: "检查SELECT字段的差异",
		CheckFunc: func(v1SQL, v2SQL string) (bool, string) {
			if !strings.Contains(v1SQL, "SELECT") {
				return false, ""
			}
			
			v1Fields := extractSelectFields(v1SQL)
			v2Fields := extractSelectFields(v2SQL)
			
			if v1Fields != v2Fields {
				return true, fmt.Sprintf("SELECT字段不一致: v1[%s] vs v2[%s]", v1Fields, v2Fields)
			}
			return false, ""
		},
	})
}

// CheckDifferences 检查SQL差异
func (cdc *CriticalDifferenceChecker) CheckDifferences(v1SQL, v2SQL string) []CriticalIssue {
	issues := make([]CriticalIssue, 0)
	
	for _, rule := range cdc.rules {
		hasIssue, description := rule.CheckFunc(v1SQL, v2SQL)
		if hasIssue {
			issues = append(issues, CriticalIssue{
				RuleName:    rule.Name,
				Description: rule.Description,
				Issue:       description,
				V1SQL:       v1SQL,
				V2SQL:       v2SQL,
				Severity:    assessIssueSeverity(rule.Name),
			})
		}
	}
	
	return issues
}

type CriticalIssue struct {
	RuleName    string
	Description string
	Issue       string
	V1SQL       string
	V2SQL       string
	Severity    string
}

func assessIssueSeverity(ruleName string) string {
	switch ruleName {
	case "DELETE_SAFETY", "UPDATE_WHERE_CONDITION":
		return "CRITICAL"
	case "JOIN_DIFFERENCE":
		return "HIGH"
	case "ORDER_BY_DIFFERENCE", "LIMIT_DIFFERENCE":
		return "MEDIUM"
	default:
		return "LOW"
	}
}

// 辅助函数：提取SQL各部分
func extractWhereClause(sql string) string {
	sql = strings.ToUpper(sql)
	whereIndex := strings.Index(sql, "WHERE")
	if whereIndex == -1 {
		return ""
	}
	
	whereClause := sql[whereIndex:]
	// 找到WHERE子句的结束位置
	endKeywords := []string{"ORDER BY", "GROUP BY", "HAVING", "LIMIT"}
	for _, keyword := range endKeywords {
		if idx := strings.Index(whereClause, keyword); idx != -1 {
			whereClause = whereClause[:idx]
			break
		}
	}
	
	return strings.TrimSpace(whereClause)
}

func extractJoins(sql string) []string {
	sql = strings.ToUpper(sql)
	joins := make([]string, 0)
	
	joinKeywords := []string{"INNER JOIN", "LEFT JOIN", "RIGHT JOIN", "FULL JOIN", "JOIN"}
	for _, keyword := range joinKeywords {
		if strings.Contains(sql, keyword) {
			// 简化处理，实际可能需要更复杂的解析
			joins = append(joins, keyword)
		}
	}
	
	return joins
}

func extractOrderBy(sql string) string {
	sql = strings.ToUpper(sql)
	orderByIndex := strings.Index(sql, "ORDER BY")
	if orderByIndex == -1 {
		return ""
	}
	
	orderByClause := sql[orderByIndex:]
	if limitIndex := strings.Index(orderByClause, "LIMIT"); limitIndex != -1 {
		orderByClause = orderByClause[:limitIndex]
	}
	
	return strings.TrimSpace(orderByClause)
}

func extractLimit(sql string) string {
	sql = strings.ToUpper(sql)
	limitIndex := strings.Index(sql, "LIMIT")
	if limitIndex == -1 {
		return ""
	}
	
	return strings.TrimSpace(sql[limitIndex:])
}

func extractSelectFields(sql string) string {
	sql = strings.ToUpper(sql)
	selectIndex := strings.Index(sql, "SELECT")
	if selectIndex == -1 {
		return ""
	}
	
	fromIndex := strings.Index(sql, "FROM")
	if fromIndex == -1 {
		return ""
	}
	
	fields := sql[selectIndex+6 : fromIndex] // 6 = len("SELECT")
	return strings.TrimSpace(fields)
}

// PrintCriticalIssues 打印关键问题
func PrintCriticalIssues(issues []CriticalIssue) {
	if len(issues) == 0 {
		log.Println("✅ 未发现关键SQL差异")
		return
	}
	
	log.Printf("🚨 发现 %d 个关键SQL差异：\n", len(issues))
	
	for _, issue := range issues {
		fmt.Printf("\n🔥 严重程度: %s\n", issue.Severity)
		fmt.Printf("   规则: %s\n", issue.RuleName)
		fmt.Printf("   描述: %s\n", issue.Description)
		fmt.Printf("   问题: %s\n", issue.Issue)
		fmt.Printf("   GORM v1: %s\n", issue.V1SQL)
		fmt.Printf("   GORM v2: %s\n", issue.V2SQL)
		fmt.Println("   " + strings.Repeat("=", 60))
	}
}
