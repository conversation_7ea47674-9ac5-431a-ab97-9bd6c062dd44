package order

import (
	"testing"
	"time"
	"zx/zx-consistency/pkg/define"

	"github.com/stretchr/testify/assert"
)

// TestCreateV2MethodFix 测试修复后的createV2方法
func TestCreateV2MethodFix(t *testing.T) {
	// 禁用SQL验证，直接测试v2方法
	validator := GetSQLValidator()
	validator.Disable()
	defer validator.Enable()

	// 准备测试数据
	orderInfo := &OrderInfo{
		OrderCode:   "TEST-FIX-CREATE-001",
		UserID:      12345,
		OrderState:  define.NOT_PAY,
		TotalMoney:  2599,
		OrderType:   POD_ORDER,
		ShopType:    ORDER_SHOP_TYPE_SHOPIFY,
		CreatedAt:   time.Now(),
		UpdatedAt:   time.Now(),
	}

	// 测试createV2方法（不传事务）
	err := orderInfo.createV2()
	assert.NoError(t, err, "createV2方法应该成功执行")
	assert.NotZero(t, orderInfo.ID, "ID应该被设置")
	assert.NotEmpty(t, orderInfo.LogState, "LogState应该被设置")

	// 清理测试数据
	cleanupTestOrderDirect(orderInfo.OrderCode)
}

// TestUpdateV2MethodFix 测试修复后的updateV2方法
func TestUpdateV2MethodFix(t *testing.T) {
	// 禁用SQL验证，直接测试v2方法
	validator := GetSQLValidator()
	validator.Disable()
	defer validator.Enable()

	// 先创建一个测试订单
	orderInfo := &OrderInfo{
		OrderCode:   "TEST-FIX-UPDATE-001",
		UserID:      12345,
		OrderState:  define.NOT_PAY,
		TotalMoney:  2599,
		OrderType:   POD_ORDER,
		ShopType:    ORDER_SHOP_TYPE_SHOPIFY,
		CreatedAt:   time.Now(),
		UpdatedAt:   time.Now(),
	}

	err := orderInfo.createV2()
	assert.NoError(t, err, "创建测试订单应该成功")
	defer cleanupTestOrderDirect(orderInfo.OrderCode)

	// 修改订单信息
	orderInfo.OrderState = define.HAVE_PAY
	orderInfo.TotalMoney = 3599
	orderInfo.PayTime = time.Now().Unix()

	// 测试updateV2方法
	err = orderInfo.updateV2()
	assert.NoError(t, err, "updateV2方法应该成功执行")
	assert.Equal(t, define.HAVE_PAY, orderInfo.OrderState, "订单状态应该被更新")
	assert.Equal(t, int32(3599), orderInfo.TotalMoney, "订单金额应该被更新")
}

// TestQueryByOrderCodeV2MethodFix 测试修复后的queryByOrderCodeV2方法
func TestQueryByOrderCodeV2MethodFix(t *testing.T) {
	// 禁用SQL验证，直接测试v2方法
	validator := GetSQLValidator()
	validator.Disable()
	defer validator.Enable()

	// 先创建一个测试订单
	testOrderCode := "TEST-FIX-QUERY-001"
	orderInfo := &OrderInfo{
		OrderCode:   testOrderCode,
		UserID:      12345,
		OrderState:  define.NOT_PAY,
		TotalMoney:  2599,
		OrderType:   POD_ORDER,
		ShopType:    ORDER_SHOP_TYPE_SHOPIFY,
		CreatedAt:   time.Now(),
		UpdatedAt:   time.Now(),
	}

	err := orderInfo.createV2()
	assert.NoError(t, err, "创建测试订单应该成功")
	defer cleanupTestOrderDirect(testOrderCode)

	// 测试查询
	queryOrder := &OrderInfo{
		OrderCode: testOrderCode,
		UserID:    12345,
	}

	err = queryOrder.queryByOrderCodeV2()
	assert.NoError(t, err, "queryByOrderCodeV2方法应该成功执行")
	assert.Equal(t, testOrderCode, queryOrder.OrderCode, "订单号应该匹配")
	assert.Equal(t, uint(12345), queryOrder.UserID, "用户ID应该匹配")
	assert.Equal(t, define.NOT_PAY, queryOrder.OrderState, "订单状态应该匹配")
}

// TestGetOrderInfoByOrderCodeV2MethodFix 测试修复后的getOrderInfoByOrderCodeV2方法
func TestGetOrderInfoByOrderCodeV2MethodFix(t *testing.T) {
	// 禁用SQL验证，直接测试v2方法
	validator := GetSQLValidator()
	validator.Disable()
	defer validator.Enable()

	// 先创建一个测试订单
	testOrderCode := "TEST-FIX-GET-INFO-001"
	orderInfo := &OrderInfo{
		OrderCode:   testOrderCode,
		UserID:      12345,
		OrderState:  define.NOT_PAY,
		TotalMoney:  2599,
		OrderType:   POD_ORDER,
		ShopType:    ORDER_SHOP_TYPE_SHOPIFY,
		CreatedAt:   time.Now(),
		UpdatedAt:   time.Now(),
	}

	err := orderInfo.createV2()
	assert.NoError(t, err, "创建测试订单应该成功")
	defer cleanupTestOrderDirect(testOrderCode)

	// 测试查询
	queryOrder := &OrderInfo{
		OrderCode: testOrderCode,
		UserID:    12345,
	}

	err = queryOrder.getOrderInfoByOrderCodeV2()
	assert.NoError(t, err, "getOrderInfoByOrderCodeV2方法应该成功执行")
	assert.Equal(t, testOrderCode, queryOrder.OrderCode, "订单号应该匹配")
	assert.Equal(t, uint(12345), queryOrder.UserID, "用户ID应该匹配")
}

// TestSQLValidationWithFixedMethods 测试修复后的SQL验证功能
func TestSQLValidationWithFixedMethods(t *testing.T) {
	// 启用SQL验证
	validator := GetSQLValidator()
	validator.Enable()
	defer validator.Disable()

	// 清理配置
	config := GetSQLValidationConfig()
	config.SetEnabled(true)

	// 测试Create方法的SQL验证
	orderInfo := &OrderInfo{
		OrderCode:   "TEST-FIX-VALIDATION-001",
		UserID:      12345,
		OrderState:  define.NOT_PAY,
		TotalMoney:  2599,
		OrderType:   POD_ORDER,
		ShopType:    ORDER_SHOP_TYPE_SHOPIFY,
		CreatedAt:   time.Now(),
		UpdatedAt:   time.Now(),
	}

	// 这应该触发SQL验证流程
	err := orderInfo.Create()
	assert.NoError(t, err, "带SQL验证的Create方法应该成功执行")
	assert.NotZero(t, orderInfo.ID, "ID应该被设置")

	// 检查是否捕获了SQL
	capturedSQLs := validator.GetCapturedSQLs("Create")
	assert.GreaterOrEqual(t, len(capturedSQLs), 1, "应该捕获到SQL语句")

	// 清理测试数据
	cleanupTestOrderDirect(orderInfo.OrderCode)
}

// TestMethodCompatibility 测试方法兼容性
func TestMethodCompatibility(t *testing.T) {
	// 测试方法签名是否保持兼容
	orderInfo := &OrderInfo{
		OrderCode:   "TEST-COMPATIBILITY-001",
		UserID:      12345,
		OrderState:  define.NOT_PAY,
		TotalMoney:  2599,
		OrderType:   POD_ORDER,
		ShopType:    ORDER_SHOP_TYPE_SHOPIFY,
	}

	// 测试不传参数的调用
	err := orderInfo.Create()
	assert.NoError(t, err, "不传参数的Create调用应该成功")
	defer cleanupTestOrderDirect(orderInfo.OrderCode)

	// 测试传nil参数的调用
	err = orderInfo.Update(nil)
	assert.NoError(t, err, "传nil参数的Update调用应该成功")

	// 测试查询方法
	queryOrder := &OrderInfo{
		OrderCode: orderInfo.OrderCode,
		UserID:    orderInfo.UserID,
	}
	err = queryOrder.QueryByOrderCode()
	assert.NoError(t, err, "QueryByOrderCode调用应该成功")

	err = queryOrder.GetOrderInfoByOrderCode()
	assert.NoError(t, err, "GetOrderInfoByOrderCode调用应该成功")
}

// cleanupTestOrderDirect 直接清理测试订单（避免触发SQL验证）
func cleanupTestOrderDirect(orderCode string) {
	// 使用原生SQL直接删除，避免触发GORM的软删除和SQL验证
	// 这里应该实现实际的清理逻辑
	// 可以使用mysql.NewConnV2().Exec("DELETE FROM order_info WHERE order_code = ?", orderCode)
}
