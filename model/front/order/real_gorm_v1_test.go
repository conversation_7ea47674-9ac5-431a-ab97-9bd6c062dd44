package order

import (
	"strings"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
)

// TestRealGormV1SQLCapture 测试真实的GORM v1 SQL捕获
func TestRealGormV1SQLCapture(t *testing.T) {
	// 创建GORM v1 SQL捕获器
	capturer := NewGormV1SQLCapturer("TestRealCapture")

	// 测试用例1：捕获CREATE SQL
	t.Run("真实CREATE SQL捕获", func(t *testing.T) {
		// 创建测试订单
		orderInfo := &OrderInfo{
			UserID:    12345,
			OrderCode: "REAL-TEST-001",
			PriceInfo: PriceInfo{
				TotalMoney: 2599,
			},
			StateInfo: StateInfo{
				OrderState: 1,
			},
			CreatedAt: time.Now(),
			UpdatedAt: time.Now(),
		}

		// 清空之前的捕获
		capturer.ClearCaptured()

		// 捕获CREATE SQL
		capturer.CaptureCreateSQL(orderInfo, nil)

		// 获取捕获的SQL
		sql, args := capturer.GetCapturedSQL()

		t.Logf("捕获的CREATE SQL: %s", sql)
		t.Logf("捕获的参数: %v", args)

		// 验证SQL不为空
		assert.NotEmpty(t, sql, "应该捕获到CREATE SQL")
		assert.Contains(t, sql, "INSERT", "应该是INSERT语句")
		assert.Contains(t, sql, "order_info", "应该包含表名")

		// 验证参数不为空
		assert.NotEmpty(t, args, "应该捕获到参数")
	})

	// 测试用例2：捕获QUERY SQL
	t.Run("真实QUERY SQL捕获", func(t *testing.T) {
		// 创建测试订单
		orderInfo := &OrderInfo{}

		// 清空之前的捕获
		capturer.ClearCaptured()

		// 捕获QUERY SQL
		conditions := map[string]interface{}{
			"order_code": "REAL-TEST-001",
			"user_id":    uint(12345),
		}
		capturer.CaptureQuerySQL(orderInfo, conditions)

		// 获取捕获的SQL
		sql, args := capturer.GetCapturedSQL()

		t.Logf("捕获的QUERY SQL: %s", sql)
		t.Logf("捕获的参数: %v", args)

		// 验证SQL不为空
		assert.NotEmpty(t, sql, "应该捕获到QUERY SQL")
		assert.Contains(t, sql, "SELECT", "应该是SELECT语句")
		assert.Contains(t, sql, "order_info", "应该包含表名")

		// 验证参数
		assert.NotEmpty(t, args, "应该捕获到参数")
	})

	// 测试用例3：捕获UPDATE SQL
	t.Run("真实UPDATE SQL捕获", func(t *testing.T) {
		// 创建测试订单
		orderInfo := &OrderInfo{}

		// 清空之前的捕获
		capturer.ClearCaptured()

		// 捕获UPDATE SQL
		conditions := map[string]interface{}{
			"id": uint(123),
		}
		updates := map[string]interface{}{
			"order_state": 2,
			"total_money": 3599,
		}
		capturer.CaptureUpdateSQL(orderInfo, conditions, updates)

		// 获取捕获的SQL
		sql, args := capturer.GetCapturedSQL()

		t.Logf("捕获的UPDATE SQL: %s", sql)
		t.Logf("捕获的参数: %v", args)

		// 验证SQL不为空
		assert.NotEmpty(t, sql, "应该捕获到UPDATE SQL")
		assert.Contains(t, sql, "UPDATE", "应该是UPDATE语句")
		assert.Contains(t, sql, "order_info", "应该包含表名")

		// 验证参数
		assert.NotEmpty(t, args, "应该捕获到参数")
	})

	// 测试用例4：捕获DELETE SQL
	t.Run("真实DELETE SQL捕获", func(t *testing.T) {
		// 创建测试订单
		orderInfo := &OrderInfo{}

		// 清空之前的捕获
		capturer.ClearCaptured()

		// 捕获DELETE SQL
		ids := []uint{123, 456, 789}
		capturer.CaptureDeleteSQL(orderInfo, ids)

		// 获取捕获的SQL
		sql, args := capturer.GetCapturedSQL()

		t.Logf("捕获的DELETE SQL: %s", sql)
		t.Logf("捕获的参数: %v", args)

		// 验证SQL不为空
		assert.NotEmpty(t, sql, "应该捕获到DELETE SQL")
		// GORM v1的删除通常是软删除，所以是UPDATE语句
		assert.True(t, 
			strings.Contains(sql, "UPDATE") || strings.Contains(sql, "DELETE"),
			"应该是UPDATE或DELETE语句")
		assert.Contains(t, sql, "order_info", "应该包含表名")

		// 验证参数
		assert.NotEmpty(t, args, "应该捕获到参数")
	})
}

// TestGormV1CallbackRegistration 测试GORM v1回调注册
func TestGormV1CallbackRegistration(t *testing.T) {
	// 创建GORM v1 SQL捕获器
	capturer := NewGormV1SQLCapturer("TestCallback")

	// 验证回调是否注册成功
	db := capturer.db

	// 检查创建回调
	createCallbacks := db.Callback().Create().Get("sql_capture:before_create")
	assert.NotNil(t, createCallbacks, "CREATE回调应该被注册")

	// 检查查询回调
	queryCallbacks := db.Callback().Query().Get("sql_capture:before_query")
	assert.NotNil(t, queryCallbacks, "QUERY回调应该被注册")

	// 检查更新回调
	updateCallbacks := db.Callback().Update().Get("sql_capture:before_update")
	assert.NotNil(t, updateCallbacks, "UPDATE回调应该被注册")

	// 检查删除回调
	deleteCallbacks := db.Callback().Delete().Get("sql_capture:before_delete")
	assert.NotNil(t, deleteCallbacks, "DELETE回调应该被注册")

	t.Log("所有GORM v1回调都已成功注册")
}

// TestGormV1VSManualSQL 对比GORM v1真实SQL和手动构建SQL
func TestGormV1VSManualSQL(t *testing.T) {
	capturer := NewGormV1SQLCapturer("TestComparison")

	t.Run("对比CREATE SQL", func(t *testing.T) {
		orderInfo := &OrderInfo{
			UserID:    12345,
			OrderCode: "COMPARE-001",
			PriceInfo: PriceInfo{
				TotalMoney: 2599,
			},
			StateInfo: StateInfo{
				OrderState: 1,
			},
		}

		// 捕获真实的GORM v1 SQL
		capturer.ClearCaptured()
		capturer.CaptureCreateSQL(orderInfo, nil)
		realSQL, realArgs := capturer.GetCapturedSQL()

		// 手动构建的SQL（之前的方式）
		manualSQL := "INSERT INTO order_info (user_id, order_code, total_money, order_state) VALUES (?, ?, ?, ?)"
		manualArgs := []interface{}{12345, "COMPARE-001", 2599, 1}

		t.Logf("真实GORM v1 SQL: %s", realSQL)
		t.Logf("真实GORM v1 参数: %v", realArgs)
		t.Logf("手动构建SQL: %s", manualSQL)
		t.Logf("手动构建参数: %v", manualArgs)

		// 对比分析
		t.Logf("SQL长度对比 - 真实: %d, 手动: %d", len(realSQL), len(manualSQL))
		t.Logf("参数数量对比 - 真实: %d, 手动: %d", len(realArgs), len(manualArgs))

		// 验证真实SQL更完整
		if len(realArgs) > len(manualArgs) {
			t.Logf("✓ 真实GORM v1 SQL包含更多字段，更准确")
		} else {
			t.Logf("⚠ 需要检查为什么真实SQL字段较少")
		}
	})
}

// TestSQLValidationWithRealGormV1 使用真实GORM v1进行SQL验证测试
func TestSQLValidationWithRealGormV1(t *testing.T) {
	validator := GetSQLValidator()
	capturer := NewGormV1SQLCapturer("TestValidation")

	t.Run("真实GORM v1 vs GORM v2对比", func(t *testing.T) {
		// 清理之前的捕获记录
		validator.ClearCapturedSQL("TestValidation")

		// 创建测试订单
		orderInfo := &OrderInfo{
			UserID:    12345,
			OrderCode: "VALIDATION-001",
			PriceInfo: PriceInfo{
				TotalMoney: 2599,
			},
			StateInfo: StateInfo{
				OrderState: 1,
			},
		}

		// 捕获真实的GORM v1 SQL
		capturer.ClearCaptured()
		capturer.CaptureCreateSQL(orderInfo, nil)
		v1SQL, v1Args := capturer.GetCapturedSQL()

		// 手动注册GORM v1 SQL到验证器
		if v1SQL != "" {
			validator.CaptureSQL("TestValidation", "v1", v1SQL, v1Args, nil)
		}

		// 模拟GORM v2 SQL（这里用手动构建的，实际应该从真实GORM v2捕获）
		v2SQL := `INSERT INTO order_info (user_id, order_code, total_money, order_state, created_at, updated_at) VALUES (12345, 'VALIDATION-001', 2599, 1, '2023-12-01 10:00:00', '2023-12-01 10:00:00')`
		v2Args := []interface{}{}
		validator.CaptureSQL("TestValidation", "v2", v2SQL, v2Args, nil)

		// 执行对比
		diff := validator.CompareAndValidate("TestValidation")

		t.Logf("真实GORM v1 SQL: %s", v1SQL)
		t.Logf("真实GORM v1 参数: %v", v1Args)
		t.Logf("GORM v2 SQL: %s", v2SQL)
		t.Logf("GORM v2 参数: %v", v2Args)

		if diff != nil {
			t.Logf("检测到差异: %+v", diff)
		} else {
			t.Logf("未检测到差异")
		}

		// 这里不做断言，因为我们主要是想看真实的差异
		t.Logf("测试完成，请查看日志了解真实的GORM v1 vs v2差异")
	})
}
