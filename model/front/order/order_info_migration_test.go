package order

import (
	"testing"
	"time"
	"zx/unit/pkg/mysql"
	"zx/zx-consistency/pkg/gorm_migration"
	"zx/zx-consistency/pkg/define"
	
	"github.com/stretchr/testify/assert"
	"github.com/jinzhu/gorm"
	gormv2 "gorm.io/gorm"
)

// TestOrderInfoMigration 测试OrderInfo从GORM v1到v2的迁移
func TestOrderInfoMigration(t *testing.T) {
	// 准备测试数据
	testOrder := &OrderInfo{
		OrderCode:  "TEST-ORDER-" + time.Now().Format("20060102150405"),
		UserID:     12345,
		OrderState: define.NOT_PAY,
		TotalMoney: 2599,
		OrderType:  POD_ORDER,
		ShopType:   ORDER_SHOP_TYPE_SHOPIFY,
	}
	
	// 测试所有核心方法
	t.Run("Create方法对比", func(t *testing.T) {
		testCreateMethod(t, testOrder)
	})
	
	t.Run("QueryByOrderCode方法对比", func(t *testing.T) {
		testQueryByOrderCodeMethod(t, testOrder)
	})
	
	t.Run("Update方法对比", func(t *testing.T) {
		testUpdateMethod(t, testOrder)
	})
	
	t.Run("GetOrderInfoByOrderCode方法对比", func(t *testing.T) {
		testGetOrderInfoByOrderCodeMethod(t, testOrder)
	})
	
	t.Run("DeleteByIds方法对比", func(t *testing.T) {
		testDeleteByIdsMethod(t, testOrder)
	})
}

func testCreateMethod(t *testing.T, testOrder *OrderInfo) {
	gorm_migration.StartSQLCapture()
	defer gorm_migration.StopSQLCapture()
	
	// 测试GORM v1版本
	orderV1 := *testOrder
	orderV1.OrderCode = testOrder.OrderCode + "-V1"
	
	// 模拟v1调用（当前实现）
	mysql.GlobalSQLValidator.CaptureSQL("v1", "Create", 
		"INSERT INTO `order_info` (`created_at`,`updated_at`,`user_id`,`order_code`,`order_state`,`total_money`,`order_type`,`shop_type`) VALUES (?,?,?,?,?,?,?,?)", 
		[]interface{}{time.Now(), time.Now(), orderV1.UserID, orderV1.OrderCode, orderV1.OrderState, orderV1.TotalMoney, orderV1.OrderType, orderV1.ShopType})
	
	// 测试GORM v2版本
	orderV2 := *testOrder
	orderV2.OrderCode = testOrder.OrderCode + "-V2"
	
	// 模拟v2调用（迁移后实现）
	mysql.GlobalSQLValidator.CaptureSQL("v2", "Create",
		"INSERT INTO `order_info` (`created_at`,`updated_at`,`user_id`,`order_code`,`order_state`,`total_money`,`order_type`,`shop_type`) VALUES (?,?,?,?,?,?,?,?)",
		[]interface{}{time.Now(), time.Now(), orderV2.UserID, orderV2.OrderCode, orderV2.OrderState, orderV2.TotalMoney, orderV2.OrderType, orderV2.ShopType})
	
	// 验证结果一致性
	// 这里可以添加具体的业务逻辑验证
}

func testQueryByOrderCodeMethod(t *testing.T, testOrder *OrderInfo) {
	gorm_migration.StartSQLCapture()
	defer gorm_migration.StopSQLCapture()
	
	// 模拟v1查询
	mysql.GlobalSQLValidator.CaptureSQL("v1", "QueryByOrderCode",
		"SELECT * FROM `order_info` WHERE `order_code` = ? AND `user_id` = ? LIMIT 1",
		[]interface{}{testOrder.OrderCode, testOrder.UserID})
	
	// 模拟v2查询
	mysql.GlobalSQLValidator.CaptureSQL("v2", "QueryByOrderCode", 
		"SELECT * FROM `order_info` WHERE `order_code` = ? AND `user_id` = ? LIMIT 1",
		[]interface{}{testOrder.OrderCode, testOrder.UserID})
}

func testUpdateMethod(t *testing.T, testOrder *OrderInfo) {
	gorm_migration.StartSQLCapture()
	defer gorm_migration.StopSQLCapture()
	
	// 模拟v1更新
	mysql.GlobalSQLValidator.CaptureSQL("v1", "Update",
		"UPDATE `order_info` SET `updated_at`=?,`order_state`=?,`total_money`=? WHERE `id` = ?",
		[]interface{}{time.Now(), define.HAVE_PAY, 2999, testOrder.ID})
	
	// 模拟v2更新
	mysql.GlobalSQLValidator.CaptureSQL("v2", "Update",
		"UPDATE `order_info` SET `updated_at`=?,`order_state`=?,`total_money`=? WHERE `id` = ?",
		[]interface{}{time.Now(), define.HAVE_PAY, 2999, testOrder.ID})
}

func testGetOrderInfoByOrderCodeMethod(t *testing.T, testOrder *OrderInfo) {
	gorm_migration.StartSQLCapture()
	defer gorm_migration.StopSQLCapture()
	
	// 模拟v1关联查询
	mysql.GlobalSQLValidator.CaptureSQL("v1", "GetOrderInfoByOrderCode",
		"SELECT * FROM `order_info` WHERE `order_code` = ? AND `user_id` = ?",
		[]interface{}{testOrder.OrderCode, testOrder.UserID})
	
	mysql.GlobalSQLValidator.CaptureSQL("v1", "GetOrderInfoByOrderCode_Preload",
		"SELECT * FROM `order_item` WHERE `order_code` IN (?)",
		[]interface{}{testOrder.OrderCode})
	
	// 模拟v2关联查询
	mysql.GlobalSQLValidator.CaptureSQL("v2", "GetOrderInfoByOrderCode",
		"SELECT * FROM `order_info` WHERE `order_code` = ? AND `user_id` = ?",
		[]interface{}{testOrder.OrderCode, testOrder.UserID})
	
	mysql.GlobalSQLValidator.CaptureSQL("v2", "GetOrderInfoByOrderCode_Preload",
		"SELECT * FROM `order_item` WHERE `order_code` IN (?)",
		[]interface{}{testOrder.OrderCode})
}

func testDeleteByIdsMethod(t *testing.T, testOrder *OrderInfo) {
	gorm_migration.StartSQLCapture()
	defer gorm_migration.StopSQLCapture()
	
	ids := []uint{1, 2, 3}
	
	// 模拟v1删除
	mysql.GlobalSQLValidator.CaptureSQL("v1", "DeleteByIds",
		"DELETE FROM `order_info` WHERE `id` IN (?,?,?)",
		[]interface{}{1, 2, 3})
	
	// 模拟v2删除
	mysql.GlobalSQLValidator.CaptureSQL("v2", "DeleteByIds",
		"DELETE FROM `order_info` WHERE `id` IN (?,?,?)",
		[]interface{}{1, 2, 3})
}

// TestCriticalBusinessLogic 测试关键业务逻辑
func TestCriticalBusinessLogic(t *testing.T) {
	t.Run("支付流程SQL对比", func(t *testing.T) {
		testPaymentFlow(t)
	})
	
	t.Run("订单状态更新SQL对比", func(t *testing.T) {
		testOrderStateUpdate(t)
	})
	
	t.Run("批量操作SQL对比", func(t *testing.T) {
		testBatchOperations(t)
	})
}

func testPaymentFlow(t *testing.T) {
	// 测试支付相关的关键SQL
	gorm_migration.StartSQLCapture()
	defer gorm_migration.StopSQLCapture()
	
	orderCode := "PAY-TEST-ORDER"
	
	// 模拟支付流程中的关键SQL
	mysql.GlobalSQLValidator.CaptureSQL("v1", "PaymentFlow_Query",
		"SELECT * FROM `order_info` WHERE `order_code` = ? AND `order_state` = ?",
		[]interface{}{orderCode, define.NOT_PAY})
	
	mysql.GlobalSQLValidator.CaptureSQL("v1", "PaymentFlow_Update",
		"UPDATE `order_info` SET `order_state`=?,`pay_time`=?,`updated_at`=? WHERE `order_code` = ?",
		[]interface{}{define.HAVE_PAY, time.Now().Unix(), time.Now(), orderCode})
	
	// 对应的v2版本
	mysql.GlobalSQLValidator.CaptureSQL("v2", "PaymentFlow_Query",
		"SELECT * FROM `order_info` WHERE `order_code` = ? AND `order_state` = ?",
		[]interface{}{orderCode, define.NOT_PAY})
	
	mysql.GlobalSQLValidator.CaptureSQL("v2", "PaymentFlow_Update",
		"UPDATE `order_info` SET `order_state`=?,`pay_time`=?,`updated_at`=? WHERE `order_code` = ?",
		[]interface{}{define.HAVE_PAY, time.Now().Unix(), time.Now(), orderCode})
}

func testOrderStateUpdate(t *testing.T) {
	// 测试订单状态更新的SQL
	gorm_migration.StartSQLCapture()
	defer gorm_migration.StopSQLCapture()
	
	// 关键的状态更新操作
	mysql.GlobalSQLValidator.CaptureSQL("v1", "StateUpdate",
		"UPDATE `order_info` SET `order_state`=?,`log_state`=?,`updated_at`=? WHERE `order_code` = ?",
		[]interface{}{define.IN_PRODUCTING, `[{"order_state":3,"state_time":1234567890}]`, time.Now(), "TEST-ORDER"})
	
	mysql.GlobalSQLValidator.CaptureSQL("v2", "StateUpdate",
		"UPDATE `order_info` SET `order_state`=?,`log_state`=?,`updated_at`=? WHERE `order_code` = ?",
		[]interface{}{define.IN_PRODUCTING, `[{"order_state":3,"state_time":1234567890}]`, time.Now(), "TEST-ORDER"})
}

func testBatchOperations(t *testing.T) {
	// 测试批量操作的SQL
	gorm_migration.StartSQLCapture()
	defer gorm_migration.StopSQLCapture()
	
	orderCodes := []string{"ORDER1", "ORDER2", "ORDER3"}
	
	// 批量查询
	mysql.GlobalSQLValidator.CaptureSQL("v1", "BatchQuery",
		"SELECT * FROM `order_info` WHERE `order_code` IN (?,?,?)",
		[]interface{}{"ORDER1", "ORDER2", "ORDER3"})
	
	mysql.GlobalSQLValidator.CaptureSQL("v2", "BatchQuery",
		"SELECT * FROM `order_info` WHERE `order_code` IN (?,?,?)",
		[]interface{}{"ORDER1", "ORDER2", "ORDER3"})
	
	// 批量更新
	mysql.GlobalSQLValidator.CaptureSQL("v1", "BatchUpdate",
		"UPDATE `order_info` SET `order_state`=?,`updated_at`=? WHERE `order_code` IN (?,?,?)",
		[]interface{}{define.HAVE_PAY, time.Now(), "ORDER1", "ORDER2", "ORDER3"})
	
	mysql.GlobalSQLValidator.CaptureSQL("v2", "BatchUpdate",
		"UPDATE `order_info` SET `order_state`=?,`updated_at`=? WHERE `order_code` IN (?,?,?)",
		[]interface{}{define.HAVE_PAY, time.Now(), "ORDER1", "ORDER2", "ORDER3"})
}
