package order

import (
	"fmt"
	"testing"

	"github.com/stretchr/testify/assert"
)

// TestEnhancedSQLComparison 测试增强的SQL对比功能
func TestEnhancedSQLComparison(t *testing.T) {
	validator := GetSQLValidator()

	// 测试用例1：参数对比
	t.Run("参数对比测试", func(t *testing.T) {
		// 模拟GORM v1和v2的SQL和参数
		v1SQL := "SELECT * FROM order_info WHERE order_code = ? AND user_id = ?"
		v1Args := []interface{}{"TEST001", 12345}
		
		v2SQL := "SELECT * FROM order_info WHERE order_code = 'TEST001' AND user_id = 12345"
		v2Args := []interface{}{} // GORM v2参数已嵌入SQL

		// 测试参数等价性
		argsEquivalent := validator.areArgsEquivalent(v1Args, v2Args, v1SQL, v2SQL)
		assert.True(t, argsEquivalent, "参数应该被认为是等价的")

		t.Logf("V1 Args: %v", v1Args)
		t.Logf("V2 Args: %v", v2Args)
		t.Logf("V2 SQL: %s", v2SQL)
		t.Logf("参数等价: %v", argsEquivalent)
	})

	// 测试用例2：INSERT结构分析
	t.Run("INSERT结构分析", func(t *testing.T) {
		v1SQL := "INSERT INTO order_info (order_code, user_id, total_money, created_at) VALUES (?, ?, ?, ?)"
		v2SQL := "INSERT INTO `order_info` (`order_code`,`user_id`,`total_money`,`created_at`) VALUES ('TEST001',12345,2599,'2023-12-01 10:00:00')"

		// 先标准化SQL，然后提取结构
		normalized1 := validator.normalizeSQL(v1SQL)
		normalized2 := validator.normalizeSQL(v2SQL)
		structure1 := validator.extractSQLStructure(normalized1)
		structure2 := validator.extractSQLStructure(normalized2)

		t.Logf("V1 SQL: %s", v1SQL)
		t.Logf("V2 SQL: %s", v2SQL)
		t.Logf("V1 标准化: %s", normalized1)
		t.Logf("V2 标准化: %s", normalized2)
		t.Logf("V1 结构: %s", structure1)
		t.Logf("V2 结构: %s", structure2)

		// 验证INSERT结构包含字段信息
		assert.Contains(t, structure1, "fields:", "V1结构应该包含字段信息")
		assert.Contains(t, structure2, "fields:", "V2结构应该包含字段信息")
		assert.Contains(t, structure1, "order_code", "应该包含order_code字段")
		assert.Contains(t, structure1, "user_id", "应该包含user_id字段")
		assert.Contains(t, structure1, "total_money", "应该包含total_money字段")
	})

	// 测试用例3：UPDATE结构分析
	t.Run("UPDATE结构分析", func(t *testing.T) {
		v1SQL := "UPDATE order_info SET order_state = ?, total_money = ?, updated_at = ? WHERE id = ?"
		v2SQL := "UPDATE order_info SET order_state = 2, total_money = 3599, updated_at = '2023-12-01 10:00:00' WHERE id = 123"

		// 先标准化SQL，然后提取结构
		normalized1 := validator.normalizeSQL(v1SQL)
		normalized2 := validator.normalizeSQL(v2SQL)
		structure1 := validator.extractSQLStructure(normalized1)
		structure2 := validator.extractSQLStructure(normalized2)

		t.Logf("V1 SQL: %s", v1SQL)
		t.Logf("V2 SQL: %s", v2SQL)
		t.Logf("V1 标准化: %s", normalized1)
		t.Logf("V2 标准化: %s", normalized2)
		t.Logf("V1 结构: %s", structure1)
		t.Logf("V2 结构: %s", structure2)

		// 验证UPDATE结构包含SET和WHERE信息
		assert.Contains(t, structure1, "set:", "应该包含SET信息")
		assert.Contains(t, structure1, "where:", "应该包含WHERE信息")
		assert.Contains(t, structure1, "order_state", "应该包含order_state字段")
		assert.Contains(t, structure1, "total_money", "应该包含total_money字段")
	})

	// 测试用例4：完整的SQL和参数对比
	t.Run("完整SQL和参数对比", func(t *testing.T) {
		// 清理之前的捕获记录
		validator.ClearCapturedSQL("TestMethod")

		// 模拟捕获GORM v1和v2的SQL
		v1SQL := "INSERT INTO order_info (order_code, user_id, total_money) VALUES (?, ?, ?)"
		v1Args := []interface{}{"TEST001", 12345, 2599}
		validator.CaptureSQL("TestMethod", "v1", v1SQL, v1Args, nil)

		v2SQL := "INSERT INTO order_info (order_code, user_id, total_money) VALUES ('TEST001', 12345, 2599)"
		v2Args := []interface{}{}
		validator.CaptureSQL("TestMethod", "v2", v2SQL, v2Args, nil)

		// 执行对比
		diff := validator.CompareAndValidate("TestMethod")
		
		// 应该没有差异
		assert.Nil(t, diff, "相同的SQL和参数不应该有差异")

		t.Logf("V1 SQL: %s, Args: %v", v1SQL, v1Args)
		t.Logf("V2 SQL: %s, Args: %v", v2SQL, v2Args)
		t.Logf("差异: %v", diff)
	})

	// 测试用例5：检测真正的参数差异
	t.Run("检测参数差异", func(t *testing.T) {
		// 清理之前的捕获记录
		validator.ClearCapturedSQL("TestDifferentArgs")

		// 模拟不同的参数
		v1SQL := "SELECT * FROM order_info WHERE order_code = ? AND user_id = ?"
		v1Args := []interface{}{"TEST001", 12345}
		validator.CaptureSQL("TestDifferentArgs", "v1", v1SQL, v1Args, nil)

		v2SQL := "SELECT * FROM order_info WHERE order_code = 'TEST002' AND user_id = 54321"
		v2Args := []interface{}{}
		validator.CaptureSQL("TestDifferentArgs", "v2", v2SQL, v2Args, nil)

		// 执行对比
		diff := validator.CompareAndValidate("TestDifferentArgs")
		
		// 应该检测到差异
		assert.NotNil(t, diff, "不同的参数应该被检测出来")

		t.Logf("V1 SQL: %s, Args: %v", v1SQL, v1Args)
		t.Logf("V2 SQL: %s, Args: %v", v2SQL, v2Args)
		t.Logf("检测到差异: %v", diff != nil)
	})
}

// TestArgExtraction 测试参数提取功能
func TestArgExtraction(t *testing.T) {
	validator := GetSQLValidator()

	testCases := []struct {
		name     string
		sql      string
		expected []interface{}
	}{
		{
			name:     "字符串和数字参数",
			sql:      "SELECT * FROM order_info WHERE order_code = 'TEST001' AND user_id = 12345",
			expected: []interface{}{"TEST001", "12345"},
		},
		{
			name:     "INSERT语句参数",
			sql:      "INSERT INTO order_info (order_code, user_id, total_money) VALUES ('TEST001', 12345, 2599)",
			expected: []interface{}{"TEST001", "12345", "2599"},
		},
		{
			name:     "时间参数",
			sql:      "UPDATE order_info SET updated_at = '2023-12-01 10:00:00' WHERE id = 123",
			expected: []interface{}{"2023-12-01 10:00:00", "123"},
		},
		{
			name:     "复杂参数",
			sql:      "SELECT * FROM order_info WHERE order_code = 'ORD-2023-001' AND user_id = 12345 AND total_money = 2599.99",
			expected: []interface{}{"ORD-2023-001", "12345", "2599.99"},
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			extracted := validator.extractArgsFromSQL(tc.sql)
			
			t.Logf("SQL: %s", tc.sql)
			t.Logf("提取的参数: %v", extracted)
			t.Logf("期望的参数: %v", tc.expected)
			
			// 验证参数数量
			assert.Equal(t, len(tc.expected), len(extracted), "参数数量应该匹配")
			
			// 验证参数值（转换为字符串对比）
			for i := 0; i < len(tc.expected) && i < len(extracted); i++ {
				expectedStr := fmt.Sprintf("%v", tc.expected[i])
				extractedStr := fmt.Sprintf("%v", extracted[i])
				assert.Equal(t, expectedStr, extractedStr, "参数值应该匹配")
			}
		})
	}
}

// TestInsertStructureExtraction 测试INSERT结构提取
func TestInsertStructureExtraction(t *testing.T) {
	validator := GetSQLValidator()

	testCases := []struct {
		name     string
		sql      string
		expected string
	}{
		{
			name:     "基本INSERT",
			sql:      "INSERT INTO order_info (order_code, user_id) VALUES (?, ?)",
			expected: "fields:order_code,user_id",
		},
		{
			name:     "带反引号的INSERT",
			sql:      "INSERT INTO `order_info` (`order_code`, `user_id`, `total_money`) VALUES (?, ?, ?)",
			expected: "fields:order_code,user_id,total_money",
		},
		{
			name:     "复杂字段INSERT",
			sql:      "INSERT INTO order_info (order_code, user_id, total_money, created_at, updated_at) VALUES (?, ?, ?, ?, ?)",
			expected: "fields:order_code,user_id,total_money,created_at,updated_at",
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			structure := validator.extractInsertStructure(tc.sql)
			
			t.Logf("SQL: %s", tc.sql)
			t.Logf("提取的结构: %s", structure)
			t.Logf("期望的结构: %s", tc.expected)
			
			// 验证包含字段信息
			assert.Contains(t, structure, "fields:", "应该包含字段信息")
			
			// 验证包含主要字段
			assert.Contains(t, structure, "order_code", "应该包含order_code字段")
			assert.Contains(t, structure, "user_id", "应该包含user_id字段")
		})
	}
}

// TestUpdateStructureExtraction 测试UPDATE结构提取
func TestUpdateStructureExtraction(t *testing.T) {
	validator := GetSQLValidator()

	testCases := []struct {
		name     string
		sql      string
		expected string
	}{
		{
			name:     "基本UPDATE",
			sql:      "UPDATE order_info SET order_state = ? WHERE id = ?",
			expected: "set:order_state;where:id = ?",
		},
		{
			name:     "多字段UPDATE",
			sql:      "UPDATE order_info SET order_state = ?, total_money = ?, updated_at = ? WHERE id = ?",
			expected: "set:order_state,total_money,updated_at;where:id = ?",
		},
		{
			name:     "复杂WHERE条件",
			sql:      "UPDATE order_info SET order_state = ? WHERE id = ? AND user_id = ?",
			expected: "set:order_state;where:id = ? AND user_id = ?",
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			// 先标准化SQL，然后提取结构
			normalizedSQL := validator.normalizeSQL(tc.sql)
			structure := validator.extractUpdateStructure(normalizedSQL)

			t.Logf("SQL: %s", tc.sql)
			t.Logf("标准化SQL: %s", normalizedSQL)
			t.Logf("提取的结构: %s", structure)
			t.Logf("期望的结构: %s", tc.expected)

			// 验证包含SET和WHERE信息
			assert.Contains(t, structure, "set:", "应该包含SET信息")
			assert.Contains(t, structure, "where:", "应该包含WHERE信息")
		})
	}
}

// TestArgValueComparison 测试参数值对比
func TestArgValueComparison(t *testing.T) {
	validator := GetSQLValidator()

	testCases := []struct {
		name     string
		arg1     interface{}
		arg2     interface{}
		expected bool
	}{
		{
			name:     "相同字符串",
			arg1:     "TEST001",
			arg2:     "TEST001",
			expected: true,
		},
		{
			name:     "不同字符串",
			arg1:     "TEST001",
			arg2:     "TEST002",
			expected: false,
		},
		{
			name:     "相同数字",
			arg1:     12345,
			arg2:     "12345",
			expected: true,
		},
		{
			name:     "不同数字",
			arg1:     12345,
			arg2:     "54321",
			expected: false,
		},
		{
			name:     "相同浮点数",
			arg1:     2599.99,
			arg2:     "2599.99",
			expected: true,
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			result := validator.isArgValueEquivalent(tc.arg1, tc.arg2)
			
			t.Logf("参数1: %v (%T)", tc.arg1, tc.arg1)
			t.Logf("参数2: %v (%T)", tc.arg2, tc.arg2)
			t.Logf("是否等价: %v", result)
			t.Logf("期望结果: %v", tc.expected)
			
			assert.Equal(t, tc.expected, result, "参数值对比结果应该匹配")
		})
	}
}
