package order

import (
	"testing"
)

// TestDebugSQLStructure 调试SQL结构提取
func TestDebugSQLStructure(t *testing.T) {
	validator := GetSQLValidator()

	// 测试INSERT结构提取
	t.Run("调试INSERT结构", func(t *testing.T) {
		originalSQL := "INSERT INTO order_info (order_code, user_id, total_money, created_at) VALUES (?, ?, ?, ?)"
		normalizedSQL := validator.normalizeSQL(originalSQL)
		
		t.Logf("原始SQL: %s", originalSQL)
		t.Logf("标准化SQL: %s", normalizedSQL)
		
		// 直接测试INSERT结构提取
		insertStructure := validator.extractInsertStructure(normalizedSQL)
		t.Logf("INSERT结构: %s", insertStructure)
		
		// 测试完整结构提取
		fullStructure := validator.extractSQLStructure(normalizedSQL)
		t.Logf("完整结构: %s", fullStructure)
	})

	// 测试UPDATE结构提取
	t.Run("调试UPDATE结构", func(t *testing.T) {
		originalSQL := "UPDATE order_info SET order_state = ? WHERE id = ?"
		normalizedSQL := validator.normalizeSQL(originalSQL)

		t.Logf("原始SQL: %s", originalSQL)
		t.Logf("标准化SQL: %s", normalizedSQL)

		// 直接测试UPDATE结构提取
		updateStructure := validator.extractUpdateStructure(normalizedSQL)
		t.Logf("UPDATE结构: %s", updateStructure)

		// 测试WHERE结构提取
		whereStructure := validator.extractWhereStructure(normalizedSQL)
		t.Logf("WHERE结构: %s", whereStructure)

		// 测试完整结构提取
		fullStructure := validator.extractSQLStructure(normalizedSQL)
		t.Logf("完整结构: %s", fullStructure)

		// 测试不使用标准化的情况
		t.Logf("--- 不使用标准化 ---")
		updateStructure2 := validator.extractUpdateStructure(originalSQL)
		t.Logf("UPDATE结构(原始): %s", updateStructure2)

		whereStructure2 := validator.extractWhereStructure(originalSQL)
		t.Logf("WHERE结构(原始): %s", whereStructure2)
	})
}
