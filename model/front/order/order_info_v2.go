package order

import (
	"encoding/json"
	"time"
	"zx/unit/pkg/ecode"
	"zx/unit/pkg/mysql"
	"zx/zx-consistency/model"
	"zx/zx-consistency/pkg/define"
	"zx/zxgo/log"

	gorm2 "gorm.io/gorm"
)

func init() {
	mysql.RegisterTable((*OrderInfoV2)(nil), mysql.OPTION_USE_INNODB_ENGINE,
		mysql.OPTION_USE_UTF8mb4_CHARSET,
		mysql.OPTION_USE_UTF8mb4_GENERAL_CI_COLLATE)
	model.SyncRegister((*OrderInfoV2)(nil))
}

// OrderInfoV2 使用 GORM v2 的订单模型
type OrderInfoV2 struct {
	ID        uint      `gorm:"primarykey" json:"id"`
	CreatedAt time.Time `json:"created_at"`
	UpdatedAt time.Time `json:"updated_at"`
	UserID    uint      `json:"user_id"`
	Level     string    `json:"level"` // 下单时，用户的会员等级

	OrderType         int    `json:"order_type"`                               //订单类型
	ShopID            uint   `json:"shop_id"`                                  // 商店id 若订单类型为THIRD_ORDER，则记录shopID和ThirdOrderCode
	ShopName          string `json:"shop_name"`                                //
	ShopNameText      string `json:"shop_name_text"`                           // 店铺名称 显示文本
	OrderCode         string `json:"order_code"  gorm:"uniqueIndex;not null;"` // 平台内订单号
	ThirdOrderCode    string `json:"third_order_code"`                         // 第三方平台订单号 若平台内订单则为空
	WorkOrderState    int    `json:"work_order_state"`                         // 工单状态 1待生成 2生成成功
	ThirdLocationID   int64  `json:"third_location_id"`                        // 第三方平台的 location_id  |shopify用的
	ThirdItemIds      string `json:"third_item_ids" gorm:"type:BLOB;"`         // 第三方平台订单子项的id，一维数组
	FulfillmentID     int64  `json:"fulfillment_id"`                           // 创建的 shopify 履约ID
	WixFulfillmentID  string `json:"wix_fulfillment_id"`                       // 创建的 wix 履约 ID
	ThirdOrderName    string `json:"third_order_name"`                         // 第三方订单名称
	ShopType          int    `json:"shop_type"  gorm:"not null"`               // 1 shopify  2etsy  3 wix
	BindFulfillment   string `json:"bind_fulfillment"`                         // 绑定的变体进行履约
	BindFulfillmentID string `json:"bind_fulfillment_id"`                      // 绑定的变体履约的ID
	AllCustomCount    int    `json:"all_custom_count"  gorm:"default:0"`       // 半自定义图层数量
	DeliveryCar       string `json:"delivery_car"`                             // 发货时分配的发货 车-排-盒

	AfterSaleCode      string `json:"after_sale_code"  gorm:"type:BLOB;"`   // 售后ID
	RetryOrderCode     string `json:"retry_order_code"  gorm:"type:BLOB;"`  // 重发订单号
	Responsible        string `json:"responsible"  gorm:"type:BLOB;"`       // 责任方
	AuditState         int    `json:"audit_state"`                          // 审核状态 1 待审核  2 审核通过  3 审核不通过
	DigitizationStatus int    `json:"digitization_status" gorm:"default:0"` // 制版任务状态 2025年3月18日17:59:55 zc 新增

	// 用于板带审核的,一个订单下存在一个板带任务审核不通过,则订单的审核状态为审核不通过

	OutboundId       int64  `json:"outbound_id" gorm:"default:0"`   // 中转的出库单 id，默认为0（即不需要中转），-1：需要中转，>0：中转出库后对应的出库单id
	SpecialState     uint64 `json:"special_state" gorm:"default:0"` // 订单特殊状态，具体参考 const OrderSpecialistState
	CopyrightRisk    int    `json:"copyright_risk"`                 // 订单版权风控状态  0 1 正常 2风控
	IsManualOverride int    `json:"is_manual_override"`             // 是否人工解除  0否 1是

	DbVersion int64 `json:"db_version" gorm:"default:0"` // 数据版本

	// 嵌入的结构体（GORM v2 版本）
	RecipientInfoV2 // 收货人信息
	PriceInfoV2     // 价格信息
	LogisticsV2     // 物流信息
	StateInfoV2     // 状态信息

	// 关联关系（GORM v2 语法）
	OrderItem []*OrderItem `json:"order_item" gorm:"foreignKey:OrderCode;references:OrderCode"`
	TrackInfo *TrackInfo   `json:"track_info" gorm:"foreignKey:OrderCode;references:OrderCode"`
}

// 嵌入结构体的v2版本
type RecipientInfoV2 struct {
	FirstName   string `json:"first_name" sensitive:"true"` //名/姓名
	LastName    string `json:"last_name" sensitive:"true"`  //姓
	CountryCode string `json:"country_code"`                //国家（国际二字码）
	Country     string `json:"country" sensitive:"true"`    //国家名称
	City        string `json:"city" sensitive:"true"`       //城市
	State       string `json:"state" sensitive:"true"`      //州/省
	District    string `json:"district" sensitive:"true"`   //区、县
	Street      string `json:"street" sensitive:"true"`     //街道/详细地址
	HouseNumber string `json:"house_number"`                //门牌号
	Company     string `json:"company"`                     //公司名
	Email       string `json:"email" sensitive:"true"`      //邮箱
	Phone       string `json:"phone" sensitive:"true"`
	Phone2      string `json:"phone2" sensitive:"true"`
	PostCode    string `json:"post_code" sensitive:"true"` //邮编
	//	税务相关
	RFC  string `json:"rfc" sensitive:"true"`  // 墨西哥 18-   MX
	CURP string `json:"curp" sensitive:"true"` // 墨西哥 18+   MX
	CPF  string `json:"cpf" sensitive:"true"`  //  巴西CPF税号  BR
}

type PriceInfoV2 struct {
	CommodityPrices       int32  `json:"commodity_prices"`                    // 商品总价格
	DigitizationPrice     int32  `json:"digitization_price" gorm:"default:0"` // 制版费用
	BlankPrice            int32  `json:"blank_price"`                         // 空白模板折扣金额
	TagPrice              int32  `json:"tag_price"`                           // 吊牌总价格
	PackPrice             int32  `json:"pack_price"`                          // 包装总价格
	TaxPrice              int32  `json:"tax_price"`                           // 税费
	LogisticsPrice        int32  `json:"logistics_price"`                     // 物流支付价格
	LogisticsZxPrice      int32  `json:"-"`                                   // 我方物流实际成本
	PwpDiscountMoney      int    `json:"pwp_discount_money"`                  // p卡折扣金额
	TotalMoney            int32  `json:"total_money"`                         // 订单总金额
	Discount              int    `json:"discount"`                            // 折扣
	DiscountID            int    `json:"discount_id"`                         // 折扣券ID
	UserDiscount          int    `json:"user_discount"`                       // 用户折扣
	VipDiscount           int    `json:"vip_discount"`                        // vip折扣
	Payment               int    `json:"payment"`                             // 支付方式
	MoneyOrder            string `json:"money_order"`                         // 第三平台订单号
	TransactionOrder      string `json:"transaction_order"`                   // 第三平台交易流水号
	PayCode               string `json:"pay_code"`                            // pod 平台支付流水号
	RefundCode            string `json:"refund_code"`                         // pod 退款流水号
	MD5Key                string `json:"md_5_key"`                            // PayPal 批量支付识别key
	MoneyOrderBatch       string `json:"money_order_batch"`                   // 批量订单号
	TransactionOrderBatch string `json:"transaction_order_batch"`             // 批量交易流水号
	OceanPaymentId        string `json:"ocean_payment_id"`                    // 钱海支付ID
	OceanPaymentStatus    string `json:"ocean_payment_status"`                // 钱海支付状态
	OceanRefundID         string `json:"ocean_refund_id"`                     // 钱海退款id
	OceanRefundResults    string `json:"ocean_refund_results"`                // 钱海退款结果
	OceanBatchOrder       string `json:"ocean_batch_order"`                   // 钱海批量支付订单id
}

type LogisticsV2 struct {
	Carrier              string `json:"carrier" gorm:"size:256"`          // 运输方
	MyLogisticsChannel   string `json:"my_logistics_channel"`             // 我们平台的物流渠道
	RefNo                string `json:"ref_no" gorm:"size:512"`           // 运单号码
	LogisticsChannelNo   string `json:"logistics_channel_no"`             // 物流渠道号码
	OdaResultSign        string `json:"oda_result_sign"`                  // ODA标识
	Weight               string `json:"weight"`                           // 克重
	RealityWeight        string `json:"reality_weight"`                   // 真实克重
	LabelURL             string `json:"label_url"`                        // 标签url
	LogisticsOrderID     string `json:"logistics_order_id" gorm:"size:512"` // YTW/LT orderID
	IsHandTK             int    `json:"is_hand_tk"`                       // 2: 手动特快
	LatestStatus         string `json:"latest_status"`                    // 物流最新状态
	LogisticsRemark      string `json:"logistics_remark" gorm:"type:BLOB;"` // 物流备注
	HandState            int    `json:"hand_state"`                       // 手动大货发货状态
	OldRefNo             string `json:"old_ref_no" gorm:"type:BLOB;"`     // 旧运单号
	LabelNo              string `json:"label_no"`                         // 标签号
	RemoteSign           bool   `json:"remote_sign"`                      // 偏远地区标识
	Identifier           string `json:"identifier" gorm:"size:12"`        // 物流账号标识
	OverseaNumber        int    `json:"oversea_number" gorm:"default:0;"` // 海外仓号
}

type StateInfoV2 struct {
	LastOrderState int    `json:"last_order_state"`            //上一个订单状态
	OrderState     int    `json:"order_state"`                 //订单状态
	LogState       string `json:"log_state" gorm:"type:BLOB;"` //订单状态变更记录
	PayTime        int64  `json:"pay_time"`                    //支付时间
	ProduceSTime   int64  `json:"produce_s_time"`              //开始生产时间
	ProduceETime   int64  `json:"produce_e_time"`              //生产完成时间
	TestingTime    int64  `json:"testing_time"`                //质检合格时间
	ShipmentsTime  int64  `json:"shipments_time"`              //发货时间
	DeliveredTime  int64  `json:"delivered_time"`              // 签收时间
	CompleteTime   int64  `json:"complete_time"`               //完成时间

	ProblemRemark  string `json:"problem_remark" gorm:"type:BLOB;"` //问题订单备注
	RefundedRemark string `json:"refunded_remark"`                  //退款订单备注
	Remark         string `json:"remark" gorm:"type:BLOB;"`         // 订单备注

	OrderPayState     int    `json:"order_pay_state"`      // 支付状态
	OrderPayStateDesc string `json:"order_pay_state_desc"` // 支付状态说明

	OceanpaymentDetail string `json:"oceanpayment_detail" gorm:"type:BLOB;"` // 钱海支付回调数据
}

func (o *OrderInfoV2) TableName() string {
	return "order_info"
}

// 新建订单 - GORM v2 版本
func (o *OrderInfoV2) Create(tx ...mysql.DBInterface) error {
	var db mysql.DBInterface
	if len(tx) != 0 {
		db = tx[0]
	} else {
		db = mysql.NewUnifiedDB()
	}

	var logStateList = make([]*LogEventState, 0, 0)
	logStateList = append(logStateList, &LogEventState{
		OrderState: o.OrderState,
		StateTime:  time.Now().Unix(),
	})

	LogListJson, err := json.Marshal(logStateList)
	if err != nil {
		return err
	}
	o.LogState = string(LogListJson)

	return db.Table(o.TableName()).Create(o).Error()
}

// 根据订单号获取订单信息 - GORM v2 版本
func (o *OrderInfoV2) GetOrderInfoByOrderCode(preload ...string) error {
	db := mysql.NewUnifiedDB().Model(o).Where("order_code = ?", o.OrderCode)

	db = db.Preload("OrderItem").Preload("OrderItem.SkuInfo")

	if len(preload) > 0 {
		for _, v := range preload {
			db = db.Preload(v)
		}
	}

	if o.UserID > 0 {
		db = db.Where("user_id = ?", o.UserID)
	}
	return db.First(o).Error()
}

// 根据订单号和用户ID获取订单信息 - GORM v2 版本
func (o *OrderInfoV2) GetOrderInfoByOrderCodeAndUserID() error {
	return mysql.NewUnifiedDB().Model(o).Where("order_code = ?", o.OrderCode).Where("user_id = ?", o.UserID).First(o).Error()
}

// 更新订单 - GORM v2 版本
func (o *OrderInfoV2) Update(tx ...mysql.DBInterface) error {
	var db mysql.DBInterface
	if len(tx) != 0 {
		db = tx[0]
	} else {
		db = mysql.NewUnifiedDB()
	}

	return db.Model(&OrderInfoV2{}).Where("id = ?", o.ID).Updates(o).First(o).Error()
}

// 带零值更新 - GORM v2 版本（工厂本地专用）
func (o *OrderInfoV2) UpdateWithZero(tx ...mysql.DBInterface) (err error) {
	var db mysql.DBInterface
	if len(tx) != 0 {
		db = tx[0]
	} else {
		db = mysql.NewUnifiedDB()
	}

	db = db.Table(o.TableName())

	// 这里的更新不能使用 Model，不然的话会有问题
	err = db.Session(&gorm2.Session{DisableNestedTransaction: true}).Select("*").Omit("updated_at").Where("id = ?", o.ID).Updates(o).Error()
	if err != nil {
		return
	}

	err = db.Where("id = ?", o.ID).Update("updated_at", o.UpdatedAt).Error()

	return
}

// 获取验证头信息 - GORM v2 版本
func (o *OrderInfoV2) GetVerifyHead(pn, ps int, idMin, idMax uint, updateTimeS, updateTimeE int64) (list []*model.VerifyHead, count int64, err error) {
	db := mysql.NewUnifiedDB().Table(o.TableName()).Select("id, UNIX_TIMESTAMP(updated_at) as updated_time")

	if idMax > 0 {
		db = db.Where("id <= ?", idMax)
	}

	if idMin > 0 {
		db = db.Where("id >= ?", idMin)
	}

	if updateTimeS > 0 {
		db = db.Where("updated_at >= FROM_UNIXTIME(?)", updateTimeS)
	}
	if updateTimeE > 0 {
		db = db.Where("updated_at <= FROM_UNIXTIME(?)", updateTimeE)
	}

	err = db.Count(&count).Error()
	if err != nil {
		return
	}

	if idMax < 1 && idMin < 1 {
		db = db.Offset((pn - 1) * ps).Limit(ps)
	}

	err = db.Find(&list).Error()

	return
}

// 创建或更新 - GORM v2 版本
func (o *OrderInfoV2) CreateOrUpdate(detail interface{}, idMapType map[uint]int) (err error) {
	detailData, err := json.Marshal(detail)
	if err != nil {
		log.Error(err)
		return
	}

	detailObj := new(OrderInfoV2)
	err = json.Unmarshal(detailData, detailObj)
	if err != nil {
		log.Error(err)
		err = ecode.WithCode(err, define.JSON_ERR, define.CodeMsgName)
		return
	}

	switch idMapType[detailObj.ID] {
	case 1:
		err = detailObj.Create()
		if err != nil {
			log.Error(err)
			err = ecode.WithCode(err, define.DB_ACCESS_ERROR, define.CodeMsgName)
			return
		}
	case 2:
		err = detailObj.UpdateWithZero()
		if err != nil {
			log.Error(err)
			err = ecode.WithCode(err, define.DB_ACCESS_ERROR, define.CodeMsgName)
			return
		}
	}
	return
}

// 获取详情列表 - GORM v2 版本
func (o *OrderInfoV2) GetDetailList(ids []uint) (interface{}, error) {
	db := mysql.NewUnifiedDB().Table(o.TableName())
	db = db.Where("id IN (?)", ids)

	list := make([]*OrderInfoV2, 0)
	err := db.Find(&list).Error()

	return list, err
}

// 根据ID列表删除 - GORM v2 版本
func (o *OrderInfoV2) DeleteByIds(ids []uint, param ...interface{}) (err error) {

	var db mysql.DBInterface

	if len(param) > 0 {
		db = param[0].(mysql.DBInterface)
	} else {
		db = mysql.NewUnifiedDB()
	}

	err = db.Model(&OrderInfoV2{}).Where("id IN (?)", ids).Delete(o).Error()
	return
}
