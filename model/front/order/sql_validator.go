package order

import (
	"context"
	"fmt"
	"regexp"
	"strconv"
	"strings"
	"sync"
	"time"
	"zx/zxgo/log"

	"gorm.io/gorm/logger"
)

// SQLValidator SQL验证器，用于对比GORM v1和v2的SQL差异
type SQLValidator struct {
	mu              sync.RWMutex
	capturedSQLs    map[string][]CapturedSQL
	enabled         bool
	alertThreshold  int // SQL差异报警阈值
	differenceCount int64
}

// CapturedSQL 捕获的SQL信息
type CapturedSQL struct {
	SQL       string
	Args      []interface{}
	Version   string // "v1" 或 "v2"
	Method    string
	Timestamp time.Time
	Error     error
}

// SQLDifference SQL差异信息
type SQLDifference struct {
	Method    string
	V1SQL     string
	V2SQL     string
	V1Args    []interface{}
	V2Args    []interface{}
	Timestamp time.Time
}

var (
	globalSQLValidator *SQLValidator
	once               sync.Once
)

// GetSQLValidator 获取全局SQL验证器实例
func GetSQLValidator() *SQLValidator {
	once.Do(func() {
		globalSQLValidator = &SQLValidator{
			capturedSQLs:   make(map[string][]CapturedSQL),
			enabled:        true, // 默认启用，生产环境可通过配置关闭
			alertThreshold: 5,    // 5次差异后报警
		}
	})
	return globalSQLValidator
}

// Enable 启用SQL验证
func (v *SQLValidator) Enable() {
	v.mu.Lock()
	defer v.mu.Unlock()
	v.enabled = true
}

// Disable 禁用SQL验证
func (v *SQLValidator) Disable() {
	v.mu.Lock()
	defer v.mu.Unlock()
	v.enabled = false
}

// IsEnabled 检查是否启用
func (v *SQLValidator) IsEnabled() bool {
	v.mu.RLock()
	defer v.mu.RUnlock()
	return v.enabled
}

// CaptureSQL 捕获SQL语句
func (v *SQLValidator) CaptureSQL(method, version, sql string, args []interface{}, err error) {
	if !v.IsEnabled() {
		return
	}

	v.mu.Lock()
	defer v.mu.Unlock()

	if v.capturedSQLs[method] == nil {
		v.capturedSQLs[method] = make([]CapturedSQL, 0, 2)
	}

	captured := CapturedSQL{
		SQL:       v.normalizeSQL(sql),
		Args:      args,
		Version:   version,
		Method:    method,
		Timestamp: time.Now(),
		Error:     err,
	}

	v.capturedSQLs[method] = append(v.capturedSQLs[method], captured)
}

// CompareAndValidate 对比并验证SQL差异
func (v *SQLValidator) CompareAndValidate(method string) *SQLDifference {
	if !v.IsEnabled() {
		return nil
	}

	v.mu.RLock()
	sqls := v.capturedSQLs[method]
	v.mu.RUnlock()

	if len(sqls) < 2 {
		return nil
	}

	var v1SQL, v2SQL *CapturedSQL
	for i := range sqls {
		if sqls[i].Version == "v1" {
			v1SQL = &sqls[i]
		} else if sqls[i].Version == "v2" {
			v2SQL = &sqls[i]
		}
	}

	if v1SQL == nil || v2SQL == nil {
		return nil
	}

	// 对比SQL语句和参数
	sqlEquivalent := v.isSQLEquivalent(v1SQL.SQL, v2SQL.SQL)
	argsEquivalent := v.areArgsEquivalent(v1SQL.Args, v2SQL.Args, v1SQL.SQL, v2SQL.SQL)

	if !sqlEquivalent || !argsEquivalent {
		difference := &SQLDifference{
			Method:    method,
			V1SQL:     v1SQL.SQL,
			V2SQL:     v2SQL.SQL,
			V1Args:    v1SQL.Args,
			V2Args:    v2SQL.Args,
			Timestamp: time.Now(),
		}

		// 记录差异并报警
		v.handleSQLDifference(difference)
		return difference
	}

	return nil
}

// normalizeSQL 标准化SQL语句，移除多余空格和换行
func (v *SQLValidator) normalizeSQL(sql string) string {
	// 移除多余的空格和换行
	re := regexp.MustCompile(`\s+`)
	normalized := re.ReplaceAllString(strings.TrimSpace(sql), " ")

	// 转换为小写进行对比
	return strings.ToLower(normalized)
}

// isSQLEquivalent 判断两个SQL语句是否等价
func (v *SQLValidator) isSQLEquivalent(sql1, sql2 string) bool {
	// 基本字符串对比
	if sql1 == sql2 {
		return true
	}

	// 标准化SQL语句进行对比
	normalized1 := v.normalizeSQLForComparison(sql1, "v1")
	normalized2 := v.normalizeSQLForComparison(sql2, "v2")

	log.Debug("对比SQL语句")
	log.Debug("v1_sql_original: ", sql1)
	log.Debug("v2_sql_original: ", sql2)
	log.Debug("v1_sql_normalized: ", normalized1)
	log.Debug("v2_sql_normalized: ", normalized2)

	// 对比核心SQL结构
	isEquivalent := v.compareSQLStructure(normalized1, normalized2)
	log.Debug("sql_equivalent: ", isEquivalent)

	return isEquivalent
}

// normalizeSQLForComparison 为对比标准化SQL语句
func (v *SQLValidator) normalizeSQLForComparison(sql, version string) string {
	// 基础标准化
	sql = v.normalizeSQL(sql)

	if version == "v2" {
		// GORM v2: 将参数值替换为占位符
		sql = v.replaceParametersWithPlaceholders(sql)
	}

	// 移除常见差异
	sql = v.removeSQLDifferences(sql)

	// 移除关联查询相关的部分（主要针对 GORM v1）
	sql = v.removeAssociationQueries(sql)

	// 移除GORM v2特有的查询优化
	sql = v.removeGormV2Optimizations(sql)

	return sql
}

// replaceParametersWithPlaceholders 将GORM v2的参数值替换为占位符
func (v *SQLValidator) replaceParametersWithPlaceholders(sql string) string {
	// 替换字符串参数 'value' -> ?
	re1 := regexp.MustCompile(`'[^']*'`)
	sql = re1.ReplaceAllString(sql, "?")

	// 替换数字参数
	re2 := regexp.MustCompile(`\b\d+\b`)
	sql = re2.ReplaceAllString(sql, "?")

	// 替换时间戳参数
	re3 := regexp.MustCompile(`\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}`)
	sql = re3.ReplaceAllString(sql, "?")

	return sql
}

// removeAssociationQueries 移除关联查询相关的SQL部分
func (v *SQLValidator) removeAssociationQueries(sql string) string {
	// 移除 LEFT JOIN 相关的关联查询（包含多个单词的表名和字段）
	re1 := regexp.MustCompile(`(?i)\s*left\s+join\s+\w+\s+on\s+[\w.]+\s*=\s*[\w.]+`)
	sql = re1.ReplaceAllString(sql, "")

	// 移除 INNER JOIN 相关的关联查询
	re2 := regexp.MustCompile(`(?i)\s*inner\s+join\s+\w+\s+on\s+[\w.]+\s*=\s*[\w.]+`)
	sql = re2.ReplaceAllString(sql, "")

	// 移除关联表的字段选择
	re3 := regexp.MustCompile(`[^\s,]+\.(order_item|track_info)\.[^\s,]+,?\s*`)
	sql = re3.ReplaceAllString(sql, "")

	// 移除INSERT语句中的关联字段（GORM v1特有问题）
	sql = v.removeAssociationFieldsFromInsert(sql)

	// 移除多余的逗号（在字段列表中）
	re4 := regexp.MustCompile(`,\s*,`)
	sql = re4.ReplaceAllString(sql, ",")

	// 移除开头或结尾的逗号
	re5 := regexp.MustCompile(`^,\s*|\s*,$`)
	sql = re5.ReplaceAllString(sql, "")

	// 清理多余的空格
	sql = regexp.MustCompile(`\s+`).ReplaceAllString(sql, " ")
	sql = strings.TrimSpace(sql)

	return sql
}

// removeAssociationFieldsFromInsert 移除INSERT语句中的关联字段
func (v *SQLValidator) removeAssociationFieldsFromInsert(sql string) string {
	// 检查是否是INSERT语句
	if !strings.Contains(strings.ToLower(sql), "insert") {
		return sql
	}

	// 移除字段列表中的关联字段
	// 匹配模式：, order_item 或 , track_info
	re1 := regexp.MustCompile(`(?i),\s*(order_item|track_info)\s*`)
	sql = re1.ReplaceAllString(sql, "")

	// 移除VALUES中对应的占位符或值
	// 这个比较复杂，因为需要计算移除的字段数量，然后移除对应数量的参数
	sql = v.removeCorrespondingValues(sql)

	return sql
}

// removeCorrespondingValues 移除VALUES中对应的参数
func (v *SQLValidator) removeCorrespondingValues(sql string) string {
	// 简化处理：如果发现关联字段被移除，我们需要移除VALUES中最后的几个参数
	// 这里假设 order_item 和 track_info 总是在字段列表的最后

	// 检查原始SQL是否包含这些字段
	originalHasOrderItem := strings.Contains(sql, "order_item")
	originalHasTrackInfo := strings.Contains(sql, "track_info")

	if !originalHasOrderItem && !originalHasTrackInfo {
		return sql // 没有关联字段，不需要处理
	}

	// 计算需要移除的参数数量
	removeCount := 0
	if originalHasOrderItem {
		removeCount++
	}
	if originalHasTrackInfo {
		removeCount++
	}

	// 移除VALUES中最后的几个参数
	if removeCount > 0 {
		sql = v.removeLastValuesParams(sql, removeCount)
	}

	return sql
}

// removeLastValuesParams 移除VALUES中最后几个参数
func (v *SQLValidator) removeLastValuesParams(sql string, count int) string {
	// 找到VALUES部分
	re := regexp.MustCompile(`(?i)values\s*\(([^)]+)\)`)
	matches := re.FindStringSubmatch(sql)
	if len(matches) < 2 {
		return sql
	}

	valuesContent := matches[1]

	// 分割参数
	params := strings.Split(valuesContent, ",")

	// 移除最后几个参数
	if len(params) > count {
		params = params[:len(params)-count]
		newValuesContent := strings.Join(params, ",")

		// 替换原来的VALUES内容
		newSQL := re.ReplaceAllString(sql, "VALUES ("+newValuesContent+")")
		return newSQL
	}

	return sql
}

// removeGormV2Optimizations 移除GORM v2特有的查询优化
func (v *SQLValidator) removeGormV2Optimizations(sql string) string {
	// 移除 ORDER BY 子句（GORM v2 自动添加的排序）
	re1 := regexp.MustCompile(`(?i)\s+order\s+by\s+[\w.\x60]+(\.\x60\w+\x60)?\s*`)
	sql = re1.ReplaceAllString(sql, "")

	// 移除 LIMIT 子句（GORM v2 的 First() 方法自动添加）
	re2 := regexp.MustCompile(`(?i)\s+limit\s+\d+\s*`)
	sql = re2.ReplaceAllString(sql, "")

	// 再次移除可能残留的 LIMIT（处理顺序问题）
	re4 := regexp.MustCompile(`(?i)limit\s+\d+\s*`)
	sql = re4.ReplaceAllString(sql, "")

	// 移除重复的 WHERE 条件（GORM v2 可能生成重复条件）
	re3 := regexp.MustCompile(`(?i)\s+and\s+[\w.\x60]+\s*=\s*[\w.\x60]+\s+and\s+[\w.\x60]+\s*=\s*[\w.\x60]+`)
	sql = re3.ReplaceAllString(sql, "")

	// 清理多余的空格
	sql = regexp.MustCompile(`\s+`).ReplaceAllString(sql, " ")
	sql = strings.TrimSpace(sql)

	return sql
}

// compareSQLStructure 对比SQL结构
func (v *SQLValidator) compareSQLStructure(sql1, sql2 string) bool {
	// 提取SQL的核心结构
	structure1 := v.extractSQLStructure(sql1)
	structure2 := v.extractSQLStructure(sql2)

	log.Debug("structure1: ", structure1)
	log.Debug("structure2: ", structure2)

	// 如果结构完全相同，直接返回true
	if structure1 == structure2 {
		return true
	}

	// 特殊处理：如果一个是UPDATE，另一个是SELECT，可能是GORM v2的Updates().First()模式
	if v.isUpdateFirstPattern(structure1, structure2) {
		return true
	}

	// 对比核心操作和表名
	return v.compareCoreSQLStructure(structure1, structure2)
}

// isUpdateFirstPattern 检查是否是GORM v2的Updates().First()模式
func (v *SQLValidator) isUpdateFirstPattern(structure1, structure2 string) bool {
	parts1 := strings.Split(structure1, "|")
	parts2 := strings.Split(structure2, "|")

	if len(parts1) < 2 || len(parts2) < 2 {
		return false
	}

	// 检查是否一个是UPDATE，另一个是SELECT，且表名相同
	if (parts1[0] == "UPDATE" && parts2[0] == "SELECT") ||
		(parts1[0] == "SELECT" && parts2[0] == "UPDATE") {
		return parts1[1] == parts2[1] // 表名相同
	}

	return false
}

// compareCoreSQLStructure 对比核心SQL结构
func (v *SQLValidator) compareCoreSQLStructure(structure1, structure2 string) bool {
	parts1 := strings.Split(structure1, "|")
	parts2 := strings.Split(structure2, "|")

	if len(parts1) < 2 || len(parts2) < 2 {
		return false
	}

	// 对比操作类型和表名
	return parts1[0] == parts2[0] && parts1[1] == parts2[1]
}

// extractSQLStructure 提取SQL的核心结构
func (v *SQLValidator) extractSQLStructure(sql string) string {
	// 提取操作类型
	var operation string
	if strings.Contains(sql, "select") {
		operation = "SELECT"
	} else if strings.Contains(sql, "insert") {
		operation = "INSERT"
	} else if strings.Contains(sql, "update") {
		operation = "UPDATE"
	} else if strings.Contains(sql, "delete") {
		operation = "DELETE"
	}

	// 提取表名
	var tableName string
	if operation == "SELECT" {
		re := regexp.MustCompile(`from\s+(\w+)`)
		matches := re.FindStringSubmatch(sql)
		if len(matches) > 1 {
			tableName = matches[1]
		}
	} else if operation == "INSERT" {
		re := regexp.MustCompile(`into\s+(\w+)`)
		matches := re.FindStringSubmatch(sql)
		if len(matches) > 1 {
			tableName = matches[1]
		}
	} else if operation == "UPDATE" {
		re := regexp.MustCompile(`update\s+(\w+)`)
		matches := re.FindStringSubmatch(sql)
		if len(matches) > 1 {
			tableName = matches[1]
		}
	}

	// 提取操作特定的结构
	var operationStructure string
	if operation == "INSERT" {
		operationStructure = v.extractInsertStructure(sql)
	} else if operation == "UPDATE" {
		operationStructure = v.extractUpdateStructure(sql)
	} else if operation == "SELECT" || operation == "DELETE" {
		operationStructure = v.extractWhereStructure(sql)
	}

	return fmt.Sprintf("%s|%s|%s", operation, tableName, operationStructure)
}

// extractInsertStructure 提取INSERT语句的结构
func (v *SQLValidator) extractInsertStructure(sql string) string {
	// 提取字段列表（忽略大小写）
	re := regexp.MustCompile(`(?i)\(([^)]+)\)\s*values`)
	matches := re.FindStringSubmatch(sql)
	if len(matches) > 1 {
		fields := matches[1]
		// 移除空格和反引号，标准化字段名
		fields = strings.ReplaceAll(fields, "`", "")
		fields = regexp.MustCompile(`\s+`).ReplaceAllString(fields, "")
		// 按字段名排序，确保顺序一致
		fieldList := strings.Split(fields, ",")
		for i := range fieldList {
			fieldList[i] = strings.TrimSpace(fieldList[i])
		}
		// 简单排序（可以使用更复杂的排序逻辑）
		return fmt.Sprintf("fields:%s", strings.Join(fieldList, ","))
	}
	return ""
}

// extractUpdateStructure 提取UPDATE语句的结构
func (v *SQLValidator) extractUpdateStructure(sql string) string {
	// 提取SET子句（忽略大小写）
	var setStructure string
	re1 := regexp.MustCompile(`(?i)set\s+(.+?)\s+where`)
	matches1 := re1.FindStringSubmatch(sql)
	if len(matches1) > 1 {
		setClause := matches1[1]
		// 提取字段名，忽略值
		re2 := regexp.MustCompile(`(\w+)\s*=`)
		fieldMatches := re2.FindAllStringSubmatch(setClause, -1)
		var fields []string
		for _, match := range fieldMatches {
			if len(match) > 1 {
				fields = append(fields, match[1])
			}
		}
		setStructure = fmt.Sprintf("set:%s", strings.Join(fields, ","))
	}

	// 提取WHERE结构
	whereStructure := v.extractWhereStructure(sql)

	if setStructure != "" && whereStructure != "" {
		return fmt.Sprintf("%s;%s", setStructure, whereStructure)
	} else if setStructure != "" {
		return setStructure
	} else {
		return whereStructure
	}
}

// extractWhereStructure 提取WHERE条件的结构
func (v *SQLValidator) extractWhereStructure(sql string) string {
	if strings.Contains(sql, "where") {
		re := regexp.MustCompile(`(?i)where\s+(.+?)(?:\s+order\s+by|\s+group\s+by|\s+limit|$)`)
		matches := re.FindStringSubmatch(sql)
		if len(matches) > 1 {
			whereClause := matches[1]
			// 将所有参数替换为占位符，只保留结构
			whereClause = regexp.MustCompile(`'[^']*'`).ReplaceAllString(whereClause, "?")
			whereClause = regexp.MustCompile(`\b\d+\.?\d*\b`).ReplaceAllString(whereClause, "?")
			whereClause = regexp.MustCompile(`\s+`).ReplaceAllString(whereClause, " ")
			return fmt.Sprintf("where:%s", strings.TrimSpace(whereClause))
		}
	}
	return ""
}

// areArgsEquivalent 对比参数是否等价
func (v *SQLValidator) areArgsEquivalent(v1Args, v2Args []interface{}, v1SQL, v2SQL string) bool {
	// 如果都没有参数，认为等价
	if len(v1Args) == 0 && len(v2Args) == 0 {
		return true
	}

	// 对于GORM v2，参数已经嵌入到SQL中，所以v2Args可能为空
	// 我们需要从SQL中提取参数进行对比
	if len(v2Args) == 0 {
		// 从GORM v2的SQL中提取参数值
		extractedArgs := v.extractArgsFromSQL(v2SQL)

		// 处理关联字段导致的参数数量差异
		adjustedV1Args := v.adjustArgsForAssociationFields(v1Args, v1SQL)

		// 如果参数数量仍然不匹配，可能是GORM版本差异导致的
		// GORM v1可能只传递非空字段，而GORM v2传递所有字段
		if len(adjustedV1Args) != len(extractedArgs) {
			log.Debug("参数数量不匹配，可能是GORM版本差异", "v1_count", len(adjustedV1Args), "v2_count", len(extractedArgs))
			// 对于INSERT操作，如果字段结构相同，我们认为参数是等价的
			if v.isInsertOperation(v1SQL) && v.isInsertOperation(v2SQL) {
				return v.compareInsertFieldStructure(v1SQL, v2SQL)
			}
		}

		return v.compareArgValues(adjustedV1Args, extractedArgs)
	}

	log.Debug("对比参数", "v1_args: ", v1Args, "v2_args: ", v2Args)
	
	// 直接对比参数
	return v.compareArgValues(v1Args, v2Args)
}

// isInsertOperation 判断是否是INSERT操作
func (v *SQLValidator) isInsertOperation(sql string) bool {
	return strings.Contains(strings.ToLower(sql), "insert")
}

// compareInsertFieldStructure 对比INSERT操作的字段结构
func (v *SQLValidator) compareInsertFieldStructure(sql1, sql2 string) bool {
	// 提取字段结构（使用完整的标准化流程）
	normalized1 := v.normalizeSQLForComparison(sql1, "v1")
	normalized2 := v.normalizeSQLForComparison(sql2, "v2")

	structure1 := v.extractInsertStructure(normalized1)
	structure2 := v.extractInsertStructure(normalized2)

	log.Debug("对比INSERT字段结构", "structure1", structure1, "structure2", structure2)

	return structure1 == structure2
}

// adjustArgsForAssociationFields 调整参数以处理关联字段差异
func (v *SQLValidator) adjustArgsForAssociationFields(args []interface{}, sql string) []interface{} {
	// 检查SQL中是否包含关联字段
	hasOrderItem := strings.Contains(sql, "order_item")
	hasTrackInfo := strings.Contains(sql, "track_info")

	if !hasOrderItem && !hasTrackInfo {
		return args // 没有关联字段，不需要调整
	}

	// 计算需要移除的参数数量（假设关联字段在最后）
	removeCount := 0
	if hasOrderItem {
		removeCount++
	}
	if hasTrackInfo {
		removeCount++
	}

	// 移除最后几个参数
	if len(args) > removeCount {
		return args[:len(args)-removeCount]
	}

	return args
}

// extractArgsFromSQL 从SQL中提取参数值（按出现顺序）
func (v *SQLValidator) extractArgsFromSQL(sql string) []interface{} {
	var args []interface{}

	// 使用正则表达式按顺序匹配所有参数
	// 匹配字符串参数 'value' 或数字参数
	re := regexp.MustCompile(`'([^']*)'|\b(\d+\.?\d*)\b`)
	matches := re.FindAllStringSubmatch(sql, -1)

	for _, match := range matches {
		if len(match) > 1 {
			if match[1] != "" {
				// 字符串参数
				args = append(args, match[1])
			} else if match[2] != "" {
				// 数字参数
				args = append(args, match[2])
			}
		}
	}

	return args
}

// compareArgValues 对比参数值
func (v *SQLValidator) compareArgValues(args1, args2 []interface{}) bool {
	if len(args1) != len(args2) {
		log.Debug("参数数量不匹配", "args1_len", len(args1), "args2_len", len(args2))
		return false
	}

	for i := 0; i < len(args1); i++ {
		if !v.isArgValueEquivalent(args1[i], args2[i]) {
			log.Debug("参数值不匹配", "index", i, "arg1", args1[i], "arg2", args2[i])
			return false
		}
	}

	return true
}

// isArgValueEquivalent 判断两个参数值是否等价
func (v *SQLValidator) isArgValueEquivalent(arg1, arg2 interface{}) bool {
	// 转换为字符串进行对比
	str1 := fmt.Sprintf("%v", arg1)
	str2 := fmt.Sprintf("%v", arg2)

	// 基本字符串对比
	if str1 == str2 {
		return true
	}

	// 忽略大小写的字符串对比
	if strings.EqualFold(str1, str2) {
		return true
	}

	// 时间格式对比（可能有不同的时间格式）
	if v.isTimeEquivalent(str1, str2) {
		return true
	}

	// 数字对比（可能有类型差异）
	if v.isNumberEquivalent(str1, str2) {
		return true
	}

	return false
}

// isTimeEquivalent 判断时间参数是否等价
func (v *SQLValidator) isTimeEquivalent(str1, str2 string) bool {
	// 尝试解析为时间
	layouts := []string{
		"2006-01-02 15:04:05",
		"2006-01-02T15:04:05Z",
		"2006-01-02T15:04:05.000Z",
		time.RFC3339,
	}

	var t1, t2 time.Time
	var err1, err2 error

	for _, layout := range layouts {
		if t1, err1 = time.Parse(layout, str1); err1 == nil {
			break
		}
	}

	for _, layout := range layouts {
		if t2, err2 = time.Parse(layout, str2); err2 == nil {
			break
		}
	}

	if err1 == nil && err2 == nil {
		// 允许1秒的时间差异
		diff := t1.Sub(t2)
		if diff < 0 {
			diff = -diff
		}
		return diff <= time.Second
	}

	return false
}

// isNumberEquivalent 判断数字参数是否等价
func (v *SQLValidator) isNumberEquivalent(str1, str2 string) bool {
	// 尝试转换为数字
	if num1, err1 := strconv.ParseFloat(str1, 64); err1 == nil {
		if num2, err2 := strconv.ParseFloat(str2, 64); err2 == nil {
			return num1 == num2
		}
	}

	return false
}

// removeSQLDifferences 移除SQL中的常见差异
func (v *SQLValidator) removeSQLDifferences(sql string) string {
	// 移除反引号
	sql = strings.ReplaceAll(sql, "`", "")

	// 移除表名前缀（如果有的话）
	re := regexp.MustCompile(`\w+\.(\w+)`)
	sql = re.ReplaceAllString(sql, "$1")

	// 标准化 WHERE 条件中的 IS NULL 和 = 0
	sql = regexp.MustCompile(`deleted_at\s*is\s*null`).ReplaceAllString(sql, "deleted_at = ?")
	sql = regexp.MustCompile(`deleted_at\s*=\s*0`).ReplaceAllString(sql, "deleted_at = ?")

	return sql
}

// handleSQLDifference 处理SQL差异
func (v *SQLValidator) handleSQLDifference(diff *SQLDifference) {
	v.differenceCount++

	// 记录差异日志
	log.Error("检测到GORM版本SQL差异",
		"method", diff.Method,
		"v1_sql", diff.V1SQL,
		"v2_sql", diff.V2SQL,
		"v1_args", diff.V1Args,
		"v2_args", diff.V2Args,
		"difference_count", v.differenceCount,
	)

	// 达到报警阈值时发送报警
	if v.differenceCount >= int64(v.alertThreshold) {
		v.sendAlert(diff)
	}
}

// sendAlert 发送报警
func (v *SQLValidator) sendAlert(diff *SQLDifference) {
	// 这里可以集成您的报警系统
	alertMsg := fmt.Sprintf(
		"GORM版本SQL差异报警 - 方法: %s, 差异次数: %d\nV1 SQL: %s\nV2 SQL: %s",
		diff.Method,
		v.differenceCount,
		diff.V1SQL,
		diff.V2SQL,
	)

	log.Error("SQL差异报警", "message", alertMsg)

	// TODO: 集成钉钉、邮件等报警系统
	// ding.SendAlert(alertMsg)
}

// ClearCapturedSQL 清理已捕获的SQL
func (v *SQLValidator) ClearCapturedSQL(method string) {
	v.mu.Lock()
	defer v.mu.Unlock()
	delete(v.capturedSQLs, method)
}

// GetCapturedSQLs 获取已捕获的SQL（用于调试）
func (v *SQLValidator) GetCapturedSQLs(method string) []CapturedSQL {
	v.mu.RLock()
	defer v.mu.RUnlock()

	sqls := v.capturedSQLs[method]
	result := make([]CapturedSQL, len(sqls))
	copy(result, sqls)
	return result
}

// SQLCaptureLogger GORM v2的SQL捕获日志器
type SQLCaptureLogger struct {
	validator *SQLValidator
	method    string
}

// NewSQLCaptureLogger 创建SQL捕获日志器
func NewSQLCaptureLogger(method string) *SQLCaptureLogger {
	return &SQLCaptureLogger{
		validator: GetSQLValidator(),
		method:    method,
	}
}

// LogMode 实现gorm.io/gorm/logger.Interface
func (l *SQLCaptureLogger) LogMode(level logger.LogLevel) logger.Interface {
	return l
}

// Info 实现gorm.io/gorm/logger.Interface
func (l *SQLCaptureLogger) Info(ctx context.Context, msg string, data ...interface{}) {
	// 不需要实现
}

// Warn 实现gorm.io/gorm/logger.Interface
func (l *SQLCaptureLogger) Warn(ctx context.Context, msg string, data ...interface{}) {
	// 不需要实现
}

// Error 实现gorm.io/gorm/logger.Interface
func (l *SQLCaptureLogger) Error(ctx context.Context, msg string, data ...interface{}) {
	// 不需要实现
}

// Trace 实现gorm.io/gorm/logger.Interface，捕获SQL
func (l *SQLCaptureLogger) Trace(ctx context.Context, begin time.Time, fc func() (string, int64), err error) {
	sql, _ := fc()
	l.validator.CaptureSQL(l.method, "v2", sql, nil, err)
}
