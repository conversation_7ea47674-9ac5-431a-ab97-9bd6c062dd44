package order

import (
	"context"
	"fmt"
	"regexp"
	"strings"
	"sync"
	"time"
	"zx/zxgo/log"

	"gorm.io/gorm/logger"
)

// SQLValidator SQL验证器，用于对比GORM v1和v2的SQL差异
type SQLValidator struct {
	mu              sync.RWMutex
	capturedSQLs    map[string][]CapturedSQL
	enabled         bool
	alertThreshold  int // SQL差异报警阈值
	differenceCount int64
}

// CapturedSQL 捕获的SQL信息
type CapturedSQL struct {
	SQL       string
	Args      []interface{}
	Version   string // "v1" 或 "v2"
	Method    string
	Timestamp time.Time
	Error     error
}

// SQLDifference SQL差异信息
type SQLDifference struct {
	Method    string
	V1SQL     string
	V2SQL     string
	V1Args    []interface{}
	V2Args    []interface{}
	Timestamp time.Time
}

var (
	globalSQLValidator *SQLValidator
	once               sync.Once
)

// GetSQLValidator 获取全局SQL验证器实例
func GetSQLValidator() *SQLValidator {
	once.Do(func() {
		globalSQLValidator = &SQLValidator{
			capturedSQLs:   make(map[string][]CapturedSQL),
			enabled:        true, // 默认启用，生产环境可通过配置关闭
			alertThreshold: 5,    // 5次差异后报警
		}
	})
	return globalSQLValidator
}

// Enable 启用SQL验证
func (v *SQLValidator) Enable() {
	v.mu.Lock()
	defer v.mu.Unlock()
	v.enabled = true
}

// Disable 禁用SQL验证
func (v *SQLValidator) Disable() {
	v.mu.Lock()
	defer v.mu.Unlock()
	v.enabled = false
}

// IsEnabled 检查是否启用
func (v *SQLValidator) IsEnabled() bool {
	v.mu.RLock()
	defer v.mu.RUnlock()
	return v.enabled
}

// CaptureSQL 捕获SQL语句
func (v *SQLValidator) CaptureSQL(method, version, sql string, args []interface{}, err error) {
	if !v.IsEnabled() {
		return
	}

	v.mu.Lock()
	defer v.mu.Unlock()

	if v.capturedSQLs[method] == nil {
		v.capturedSQLs[method] = make([]CapturedSQL, 0, 2)
	}

	captured := CapturedSQL{
		SQL:       v.normalizeSQL(sql),
		Args:      args,
		Version:   version,
		Method:    method,
		Timestamp: time.Now(),
		Error:     err,
	}

	v.capturedSQLs[method] = append(v.capturedSQLs[method], captured)
}

// CompareAndValidate 对比并验证SQL差异
func (v *SQLValidator) CompareAndValidate(method string) *SQLDifference {
	if !v.IsEnabled() {
		return nil
	}

	v.mu.RLock()
	sqls := v.capturedSQLs[method]
	v.mu.RUnlock()

	if len(sqls) < 2 {
		return nil
	}

	var v1SQL, v2SQL *CapturedSQL
	for i := range sqls {
		if sqls[i].Version == "v1" {
			v1SQL = &sqls[i]
		} else if sqls[i].Version == "v2" {
			v2SQL = &sqls[i]
		}
	}

	if v1SQL == nil || v2SQL == nil {
		return nil
	}

	// 对比SQL语句
	if !v.isSQLEquivalent(v1SQL.SQL, v2SQL.SQL) {
		difference := &SQLDifference{
			Method:    method,
			V1SQL:     v1SQL.SQL,
			V2SQL:     v2SQL.SQL,
			V1Args:    v1SQL.Args,
			V2Args:    v2SQL.Args,
			Timestamp: time.Now(),
		}

		// 记录差异并报警
		v.handleSQLDifference(difference)
		return difference
	}

	return nil
}

// normalizeSQL 标准化SQL语句，移除多余空格和换行
func (v *SQLValidator) normalizeSQL(sql string) string {
	// 移除多余的空格和换行
	re := regexp.MustCompile(`\s+`)
	normalized := re.ReplaceAllString(strings.TrimSpace(sql), " ")

	// 转换为小写进行对比
	return strings.ToLower(normalized)
}

// isSQLEquivalent 判断两个SQL语句是否等价
func (v *SQLValidator) isSQLEquivalent(sql1, sql2 string) bool {
	// 基本字符串对比
	if sql1 == sql2 {
		return true
	}

	// 移除可能的差异（如表名前缀、引号等）
	sql1 = v.removeSQLDifferences(sql1)
	sql2 = v.removeSQLDifferences(sql2)

	return sql1 == sql2
}

// removeSQLDifferences 移除SQL中的常见差异
func (v *SQLValidator) removeSQLDifferences(sql string) string {
	// 移除反引号
	sql = strings.ReplaceAll(sql, "`", "")

	// 移除表名前缀（如果有的话）
	re := regexp.MustCompile(`\w+\.(\w+)`)
	sql = re.ReplaceAllString(sql, "$1")

	// 标准化 WHERE 条件中的 IS NULL 和 = 0
	sql = regexp.MustCompile(`deleted_at\s*is\s*null`).ReplaceAllString(sql, "deleted_at = 0")

	return sql
}

// handleSQLDifference 处理SQL差异
func (v *SQLValidator) handleSQLDifference(diff *SQLDifference) {
	v.differenceCount++

	// 记录差异日志
	log.Error("检测到GORM版本SQL差异",
		"method", diff.Method,
		"v1_sql", diff.V1SQL,
		"v2_sql", diff.V2SQL,
		"v1_args", diff.V1Args,
		"v2_args", diff.V2Args,
		"difference_count", v.differenceCount,
	)

	// 达到报警阈值时发送报警
	if v.differenceCount >= int64(v.alertThreshold) {
		v.sendAlert(diff)
	}
}

// sendAlert 发送报警
func (v *SQLValidator) sendAlert(diff *SQLDifference) {
	// 这里可以集成您的报警系统
	alertMsg := fmt.Sprintf(
		"GORM版本SQL差异报警 - 方法: %s, 差异次数: %d\nV1 SQL: %s\nV2 SQL: %s",
		diff.Method,
		v.differenceCount,
		diff.V1SQL,
		diff.V2SQL,
	)

	log.Error("SQL差异报警", "message", alertMsg)

	// TODO: 集成钉钉、邮件等报警系统
	// ding.SendAlert(alertMsg)
}

// ClearCapturedSQL 清理已捕获的SQL
func (v *SQLValidator) ClearCapturedSQL(method string) {
	v.mu.Lock()
	defer v.mu.Unlock()
	delete(v.capturedSQLs, method)
}

// GetCapturedSQLs 获取已捕获的SQL（用于调试）
func (v *SQLValidator) GetCapturedSQLs(method string) []CapturedSQL {
	v.mu.RLock()
	defer v.mu.RUnlock()

	sqls := v.capturedSQLs[method]
	result := make([]CapturedSQL, len(sqls))
	copy(result, sqls)
	return result
}

// SQLCaptureLogger GORM v2的SQL捕获日志器
type SQLCaptureLogger struct {
	validator *SQLValidator
	method    string
}

// NewSQLCaptureLogger 创建SQL捕获日志器
func NewSQLCaptureLogger(method string) *SQLCaptureLogger {
	return &SQLCaptureLogger{
		validator: GetSQLValidator(),
		method:    method,
	}
}

// LogMode 实现gorm.io/gorm/logger.Interface
func (l *SQLCaptureLogger) LogMode(level logger.LogLevel) logger.Interface {
	return l
}

// Info 实现gorm.io/gorm/logger.Interface
func (l *SQLCaptureLogger) Info(ctx context.Context, msg string, data ...interface{}) {
	// 不需要实现
}

// Warn 实现gorm.io/gorm/logger.Interface
func (l *SQLCaptureLogger) Warn(ctx context.Context, msg string, data ...interface{}) {
	// 不需要实现
}

// Error 实现gorm.io/gorm/logger.Interface
func (l *SQLCaptureLogger) Error(ctx context.Context, msg string, data ...interface{}) {
	// 不需要实现
}

// Trace 实现gorm.io/gorm/logger.Interface，捕获SQL
func (l *SQLCaptureLogger) Trace(ctx context.Context, begin time.Time, fc func() (string, int64), err error) {
	sql, _ := fc()
	l.validator.CaptureSQL(l.method, "v2", sql, nil, err)
}
