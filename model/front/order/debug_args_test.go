package order

import (
	"testing"
)

// TestDebugArgsExtraction 调试参数提取
func TestDebugArgsExtraction(t *testing.T) {
	validator := GetSQLValidator()

	// 测试原始SQL的参数提取
	t.Run("原始SQL参数提取", func(t *testing.T) {
		originalSQL := `INSERT INTO order_info (created_at, updated_at, user_id, order_code, total_money) VALUES ('2023-12-01 10:00:00', '2023-12-01 10:00:00', 12345, 'TEST001', 2599)`
		
		extractedArgs := validator.extractArgsFromSQL(originalSQL)
		
		t.Logf("原始SQL: %s", originalSQL)
		t.Logf("提取的参数: %v", extractedArgs)
		t.Logf("参数数量: %d", len(extractedArgs))
		
		for i, arg := range extractedArgs {
			t.Logf("参数[%d]: %v (%T)", i, arg, arg)
		}
	})

	// 测试标准化后SQL的参数提取
	t.Run("标准化SQL参数提取", func(t *testing.T) {
		originalSQL := `INSERT INTO order_info (created_at, updated_at, user_id, order_code, total_money) VALUES ('2023-12-01 10:00:00', '2023-12-01 10:00:00', 12345, 'TEST001', 2599)`
		normalizedSQL := validator.normalizeSQL(originalSQL)
		
		extractedArgs := validator.extractArgsFromSQL(normalizedSQL)
		
		t.Logf("原始SQL: %s", originalSQL)
		t.Logf("标准化SQL: %s", normalizedSQL)
		t.Logf("提取的参数: %v", extractedArgs)
		t.Logf("参数数量: %d", len(extractedArgs))
		
		for i, arg := range extractedArgs {
			t.Logf("参数[%d]: %v (%T)", i, arg, arg)
		}
	})

	// 测试参数对比
	t.Run("参数对比测试", func(t *testing.T) {
		v1Args := []interface{}{"2023-12-01 10:00:00", "2023-12-01 10:00:00", 12345, "TEST001", 2599}
		v2SQL := `INSERT INTO order_info (created_at, updated_at, user_id, order_code, total_money) VALUES ('2023-12-01 10:00:00', '2023-12-01 10:00:00', 12345, 'TEST001', 2599)`
		
		extractedArgs := validator.extractArgsFromSQL(v2SQL)
		
		t.Logf("V1 参数: %v", v1Args)
		t.Logf("V2 SQL: %s", v2SQL)
		t.Logf("V2 提取参数: %v", extractedArgs)
		
		isEquivalent := validator.compareArgValues(v1Args, extractedArgs)
		t.Logf("参数等价: %v", isEquivalent)
		
		// 逐个对比参数
		for i := 0; i < len(v1Args) && i < len(extractedArgs); i++ {
			argEquivalent := validator.isArgValueEquivalent(v1Args[i], extractedArgs[i])
			t.Logf("参数[%d] %v vs %v: %v", i, v1Args[i], extractedArgs[i], argEquivalent)
		}
	})
}
