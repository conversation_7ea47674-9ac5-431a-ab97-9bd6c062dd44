package order

import (
	"encoding/json"
	"fmt"
	"github.com/360EntSecGroup-Skylar/excelize"
	"io/ioutil"
	"net/http"
	"os"
	"strings"
	"testing"
	"time"
	"zx/unit/pkg/ecode"
	"zx/unit/pkg/mysql"
	"zx/zx-consistency/model"
	"zx/zx-consistency/service/deliver"
	"zx/zxgo/httplib"
	"zx/zxgo/log"
)

// 生产环境
var mysqlCfg = mysql.Config{
	Addr:         "product.cuhd7h3xijsl.us-east-2.rds.amazonaws.com:3306",
	Username:     "admin",
	Password:     "QvOO9jMXPFwR314VCvu0",
	Database:     "pod_partner",
	ShowSQL:      true,
	MaxIdleInter: 2,
}

// 测试环境
//var mysqlCfg = mysql.Config{
//	Addr:         "database-test.cuhd7h3xijsl.us-east-2.rds.amazonaws.com:3306",
//	Username:     "admin",
//	Password:     "8tCOHSwSTvX7RepsdwK2",
//	Database:     "pod_partner",
//	ShowSQL:      true,
//	MaxIdleInter: 2,
//}

var CmapL map[string]*model.Color

func TestMain(m *testing.M) {
	if err := mysql.Init(&mysqlCfg); err != nil {
		fmt.Println("数据库连接失败")
		return
	}

	colorDB := model.Color{}
	Cmap := make(map[string]*model.Color)

	list, err := colorDB.Query()
	if err != nil {
		err = ecode.WithCode(err, ecode.CODE_DB_ACCESS_ERROR)
		fmt.Println(err)
	}
	for _, v := range list {
		Cmap[v.ColorId] = v
	}

	CmapL = Cmap

	m.Run()
}

// 测试
func TestOrderInfo_GetOrderNotDelivery(t *testing.T) {

	getOrderToFulfillPaypal(t)
	//getWalletLogToFulfillPaypal(t)

	orderDB := OrderInfo{
		RecipientInfo: RecipientInfo{
			CountryCode: "US",
		},
		Logistics: Logistics{
			Carrier: "YTW",
		},
	}
	orderList, err := orderDB.QueryAllNotDelivery()
	if err != nil {
		t.Error(err)
		return
	}
	for _, v := range orderList {
		t.Log("order code: ", v.OrderCode)
	}
	return
}

func getOrderToFulfillPaypal(t *testing.T) {
	orderDB0 := OrderInfo{}
	orderList0, count, err := orderDB0.QueryToShippedPaypal()
	t.Log("count: ", count)
	if err != nil {
		t.Error(err)
		return
	}

	orderListData, _ := json.Marshal(orderList0)
	t.Log("orderListData: ", string(orderListData))

	for _, oneOrderInfo := range orderList0 {

		fUrl := fmt.Sprintf("https://www.podpartner.com/api/factory/v1/code/test_paypal_shopped/%s", oneOrderInfo.OrderCode)
		req := httplib.Put(fUrl).
			Header("Content-Type", "application/json").
			Header("Accept", "application/json")
		t.Log("oneOrderInfo.OrderCode: ", oneOrderInfo.OrderCode)
		resp, err := req.Response()

		time.Sleep(500 * time.Millisecond)

		if err != nil || resp.StatusCode != http.StatusOK {
			t.Error(">>>>>>>>>>")
			t.Error(err)
			if resp != nil {
				t.Error("resp.StatusCode: ", resp.StatusCode)
			}
			t.Error("<<<<<<<<<")
			//return
			continue
		}

		// 解析响应
		data, err := ioutil.ReadAll(resp.Body)
		if err != nil {
			t.Error(err)
			continue
		}
		t.Log("调用成功: ", string(data))
		t.Log("\n")
	}
	return
}

func TestGetOrderItemCountByPay(t *testing.T) {
	orderCodeDB := OrderInfo{}

	orderCodeList, err := orderCodeDB.GetOrderCodeByPay()
	if err != nil {
		t.Error(err)
		return
	}

	t.Log(len(orderCodeList))
	t.Log(orderCodeList)

	orderItemDB := OrderItem{}

	list, err := orderItemDB.GetFinanceByOrderCode(orderCodeList)
	if err != nil {
		t.Error(err)
		return
	}

	fWrite := excelize.NewFile()
	// 创建一个工作表
	index := fWrite.NewSheet("Sheet1")
	// 设置工作簿的默认工作表
	fWrite.SetActiveSheet(index)
	// 根据指定路径保存文件

	fWrite.SetCellValue("Sheet1", "A1", "平台SKU")
	fWrite.SetCellValue("Sheet1", "B1", "颜色中文名")
	fWrite.SetCellValue("Sheet1", "C1", "尺码")
	fWrite.SetCellValue("Sheet1", "D1", "销售数量")
	fWrite.SetCellValue("Sheet1", "E1", "销售单价")
	fWrite.SetCellValue("Sheet1", "F1", "销售总金额")
	fWrite.SetCellValue("Sheet1", "G1", "吊牌总金额")
	fWrite.SetCellValue("Sheet1", "H1", "包装总金额")

	for i, v := range list {
		colorID := getColorID(v.SKU)
		if len(colorID) < 3 {
			t.Log(v, "查不到对应的")
			continue
		}
		colorCname := CmapL[colorID].CName

		fWrite.SetCellValue("Sheet1", fmt.Sprintf("A%d", i+2), v.SKU)
		fWrite.SetCellValue("Sheet1", fmt.Sprintf("B%d", i+2), colorCname)
		fWrite.SetCellValue("Sheet1", fmt.Sprintf("C%d", i+2), v.Size)
		fWrite.SetCellValue("Sheet1", fmt.Sprintf("D%d", i+2), v.Total)
		fWrite.SetCellValue("Sheet1", fmt.Sprintf("E%d", i+2), v.Price)
		fWrite.SetCellValue("Sheet1", fmt.Sprintf("F%d", i+2), v.TotalMoney)
		fWrite.SetCellValue("Sheet1", fmt.Sprintf("G%d", i+2), v.TagPrice)
		fWrite.SetCellValue("Sheet1", fmt.Sprintf("H%d", i+2), v.PackPrice)
	}

	if err := fWrite.SaveAs("test.xlsx"); err != nil {
		t.Error(err)
	}

}

func getColorID(sku string) string {
	if len(sku) < 11 {
		return ""
	}
	colorID := sku[7:10]
	return colorID
}

// 检索所有 已支付，未发货 拣车号重复的订单
func TestOrderInfo_QueryAll(t *testing.T) {

	conditionMap := make([]map[string]interface{}, 0)

	condition1 := make(map[string]interface{})

	//orderStates := make([]int, 0)
	//orderStates = append(orderStates, 2)
	//orderStates = append(orderStates, 3)
	//orderStates = append(orderStates, 4)
	//orderStates = append(orderStates, 5)
	condition1["order_state"] = fmt.Sprintf("%d,%d,%d,%d,%d", 2, 3, 4, 5, 6)

	conditionMap = append(conditionMap, condition1)

	orderCodeDB := OrderInfo{}
	orderInfoList, err := orderCodeDB.QueryListByMap(conditionMap)
	if err != nil {
		t.Error(err)
		return
	}

	orderInfoListData, err := json.Marshal(orderInfoList)
	if err != nil {
		t.Error(err)
		return
	}

	boxNumMap := make(map[string]int)
	for _, oneOrderInfo := range orderInfoList {
		if len(oneOrderInfo.DeliveryCar) > 0 {
			boxNumMap[oneOrderInfo.DeliveryCar]++
			t.Log("oneOrderInfo.DeliveryCar: ", oneOrderInfo.DeliveryCar)
		}
	}

	log.Debug("boxNumMap: ", boxNumMap)

	for _, oneOrderInfo := range orderInfoList {
		if boxNumMap[oneOrderInfo.DeliveryCar] > 1 {
			t.Logf("order code: %s, boxNum: %s, count: %d \n", oneOrderInfo.OrderCode, oneOrderInfo.DeliveryCar, boxNumMap[oneOrderInfo.DeliveryCar])
		}
	}

	t.Log("orderInfoList len: ", len(orderInfoList))
	t.Log("orderInfoList: ", string(orderInfoListData))

	return
}

// 统计每个月的销售额
func TestSalesStatistic(t *testing.T) {

	destDir := "C:\\Users\\<USER>\\Desktop"

	layout := "2006-01-02 15:04:05"
	timeStr := "2023-03-01 00:00:00"

	tCurMonth, err := time.ParseInLocation(layout, timeStr, time.Local)
	if err != nil {
		t.Error(err)
		return
	}
	tNextMonth := tCurMonth.AddDate(0, 1, 0)

	for true {
		orderInfoDB := OrderInfo{}
		orderInfoList, err := orderInfoDB.QueryByPaymentTimeQuantum(tCurMonth.Unix(), tNextMonth.Unix())
		if err != nil {
			t.Error(err)
			return
		}

		// 将数据整理为 map
		skuStatistic := make(map[string]int)
		for _, oneOrderInfo := range orderInfoList {
			for _, oneItem := range oneOrderInfo.OrderItem {
				skuStatistic[oneItem.SKU]++
			}
		}

		fWrite := excelize.NewFile()
		// 创建一个工作表
		index := fWrite.NewSheet("Sheet1")
		// 设置工作簿的默认工作表
		fWrite.SetActiveSheet(index)
		// 根据指定路径保存文件
		fWrite.SetCellValue("Sheet1", "A1", "平台SKU")
		fWrite.SetCellValue("Sheet1", "B1", "销售数量")

		i := 0
		for sku, count := range skuStatistic {
			fWrite.SetCellValue("Sheet1", fmt.Sprintf("A%d", i+2), sku)
			fWrite.SetCellValue("Sheet1", fmt.Sprintf("B%d", i+2), count)
			i++
		}

		filePath := fmt.Sprintf("%s\\%s.xlsx", destDir, tCurMonth.Format("2006-01"))

		t.Log("filePath: ", filePath)

		if err := fWrite.SaveAs(filePath); err != nil {
			t.Error(err)
		}

		if int(tCurMonth.Month()) >= int(time.Now().Month()) && tCurMonth.Day() >= time.Now().Day() {
			break
		}
		tCurMonth = tNextMonth
		tNextMonth = tCurMonth.AddDate(0, 1, 0)
	}

	return
}

// 根据运单号，统计运单申报的SKU
func TestOrderByRefNo(t *testing.T) {
	refList := make([]string, 0, 0)
	//打开excel
	exlFile, err := os.Open("EQ.xlsx")
	if err != nil {
		t.Error(err)
		return
	}

	f, err := excelize.OpenReader(exlFile)

	if err != nil {
		t.Error(err)
		return
	}

	sheetMap := f.GetSheetMap()

	for _, v := range sheetMap {
		rows := f.GetRows(v)
		for numRow := 1; numRow < len(rows); numRow++ {
			if len(rows[numRow]) < 1 {
				continue
			}
			refList = append(refList, rows[numRow][0])
		}
	}

	orderCodeDB := OrderInfo{}
	list, err := orderCodeDB.GetOrderByRefNo(refList)
	if err != nil {
		t.Error(err)
		return
	}

	// 写入数据
	fWrite := excelize.NewFile()
	// 创建一个工作表
	index := fWrite.NewSheet("Sheet1")
	// 设置工作簿的默认工作表
	fWrite.SetActiveSheet(index)
	fWrite.SetCellValue("Sheet1", "A1", "EQ运单号")
	fWrite.SetCellValue("Sheet1", "B1", "SKU")

	for i, v := range list {
		fWrite.SetCellValue("Sheet1", fmt.Sprintf("A%d", i+2), v.RefNo)
		skuList := make([]string, 0, 0)
		for _, item := range v.OrderItem {
			skuList = append(skuList, item.SKU)
		}
		// 将skuList 使用,拼接成字符串sku
		sku := strings.Join(skuList, ",")

		fWrite.SetCellValue("Sheet1", fmt.Sprintf("B%d", i+2), sku)
	}

	if err := fWrite.SaveAs("test.xlsx"); err != nil {
		t.Error(err)
	}
}

func TestLetian(t *testing.T)  {
	orderDB := OrderInfo{}
	orderDB.Carrier = "CNE"
	orderDB.CountryCode="US"

	list, err := orderDB.QueryAllNotDelivery()
	if err != nil {
		t.Log(err)
	}

	//t.Log(len(list))

	orderCodeList := make([]string, 0, 0)
	errOrderCodeList := make([]string, 0, 0)

	for _, v := range list {
		t.Log(v.OrderCode)
	//	使用订单信息，重新生成运单
		deliverInterface, err := deliver.GetDeliver(v.OrderCode)
		if err != nil {
			t.Error(err)
			errOrderCodeList = append(errOrderCodeList, v.OrderCode)
			continue
		}
		err = deliverInterface.CreatWaybill(true)
		if err != nil {
			t.Error(err)
			errOrderCodeList = append(errOrderCodeList, v.OrderCode)
			continue
		}

		//保存refNo到orderDB
		err = deliverInterface.SaveRefNo(true, "")
		if err != nil {
			t.Error(err)
			errOrderCodeList = append(errOrderCodeList, v.OrderCode)
			continue
		}
		orderCodeList = append(orderCodeList, v.OrderCode)
	}

	t.Log("失败的：",errOrderCodeList)
	t.Log("成功的：",orderCodeList)
}