package order

import (
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
)

// TestSQLValidationCreate 测试Create方法的SQL验证
func TestSQLValidationCreate(t *testing.T) {

	// 初始化测试环境
	InitMysql()

	// 启用SQL验证
	manager := GetSQLValidationManager()
	manager.StartValidation()
	defer manager.StopValidation()

	// 准备测试数据
	orderInfo := &OrderInfo{
		OrderCode: "TEST-CREATE-001",
		UserID:    12345,
		OrderType: POD_ORDER,
		ShopType:  ORDER_SHOP_TYPE_SHOPIFY,
		CreatedAt: time.Now(),
		UpdatedAt: time.Now(),
		StateInfo: StateInfo{
			OrderState: NOT_PAY,
		},
		PriceInfo: PriceInfo{
			TotalMoney: 2599,
		},
	}

	// 执行Create操作
	err := orderInfo.Create()

	// 验证操作成功
	assert.NoError(t, err, "Create操作应该成功")
	assert.NotZero(t, orderInfo.ID, "ID应该被设置")

	// 检查SQL验证结果
	validator := GetSQLValidator()
	capturedSQLs := validator.GetCapturedSQLs("Create")

	// 应该捕获到v1和v2的SQL
	assert.GreaterOrEqual(t, len(capturedSQLs), 1, "应该捕获到SQL语句")

	// 验证SQL中包含预期的表名和字段
	found := false
	for _, sql := range capturedSQLs {

		t.Log(sql.Version, ": ", sql.SQL)

		if sql.Version == "v2" {
			assert.Contains(t, sql.SQL, "order_info", "SQL应该包含表名")
			assert.Contains(t, sql.SQL, "insert", "应该是INSERT语句")
			found = true
			break
		}
	}
	assert.True(t, found, "应该找到v2版本的SQL")

	// 清理测试数据
	cleanupTestOrder(orderInfo.OrderCode)
}

// TestSQLValidationQueryByOrderCode 测试QueryByOrderCode方法的SQL验证
func TestSQLValidationQueryByOrderCode(t *testing.T) {
	// 启用SQL验证
	manager := GetSQLValidationManager()
	manager.StartValidation()
	defer manager.StopValidation()

	// 准备测试数据
	testOrderCode := "TEST-QUERY-001"
	setupTestOrder(testOrderCode, 12345)
	defer cleanupTestOrder(testOrderCode)

	// 执行查询操作
	orderInfo := &OrderInfo{
		OrderCode: testOrderCode,
		UserID:    12345,
	}

	err := orderInfo.QueryByOrderCode()

	// 验证操作成功
	assert.NoError(t, err, "QueryByOrderCode操作应该成功")
	assert.Equal(t, testOrderCode, orderInfo.OrderCode, "订单号应该匹配")

	// 检查SQL验证结果
	validator := GetSQLValidator()
	capturedSQLs := validator.GetCapturedSQLs("QueryByOrderCode")

	// 验证捕获的SQL
	found := false
	for _, sql := range capturedSQLs {
		if sql.Version == "v2" {
			assert.Contains(t, sql.SQL, "SELECT", "应该是SELECT语句")
			assert.Contains(t, sql.SQL, "order_info", "SQL应该包含表名")
			assert.Contains(t, sql.SQL, "order_code", "SQL应该包含order_code条件")
			found = true
			break
		}
	}
	assert.True(t, found, "应该找到v2版本的SQL")
}

// TestSQLValidationUpdate 测试Update方法的SQL验证
func TestSQLValidationUpdate(t *testing.T) {
	// 启用SQL验证
	manager := GetSQLValidationManager()
	manager.StartValidation()
	defer manager.StopValidation()

	// 准备测试数据
	testOrderCode := "TEST-UPDATE-001"
	orderID := setupTestOrder(testOrderCode, 12345)
	defer cleanupTestOrder(testOrderCode)

	// 执行更新操作
	orderInfo := &OrderInfo{
		ID: orderID,
		StateInfo: StateInfo{
			OrderState: HAVE_PAY,
		},
		PriceInfo: PriceInfo{
			TotalMoney: 3599,
		},
	}

	err := orderInfo.Update()

	// 验证操作成功
	assert.NoError(t, err, "Update操作应该成功")

	// 检查SQL验证结果
	validator := GetSQLValidator()
	capturedSQLs := validator.GetCapturedSQLs("Update")

	// 验证捕获的SQL
	found := false
	for _, sql := range capturedSQLs {
		if sql.Version == "v2" {
			assert.Contains(t, sql.SQL, "UPDATE", "应该是UPDATE语句")
			assert.Contains(t, sql.SQL, "order_info", "SQL应该包含表名")
			found = true
			break
		}
	}
	assert.True(t, found, "应该找到v2版本的SQL")
}

// TestSQLValidationGetOrderInfoByOrderCode 测试GetOrderInfoByOrderCode方法的SQL验证
func TestSQLValidationGetOrderInfoByOrderCode(t *testing.T) {
	// 启用SQL验证
	manager := GetSQLValidationManager()
	manager.StartValidation()
	defer manager.StopValidation()

	// 准备测试数据
	testOrderCode := "TEST-GET-INFO-001"
	setupTestOrder(testOrderCode, 12345)
	defer cleanupTestOrder(testOrderCode)

	// 执行查询操作
	orderInfo := &OrderInfo{
		OrderCode: testOrderCode,
		UserID:    12345,
	}

	err := orderInfo.GetOrderInfoByOrderCode()

	// 验证操作成功
	assert.NoError(t, err, "GetOrderInfoByOrderCode操作应该成功")

	// 检查SQL验证结果
	validator := GetSQLValidator()
	capturedSQLs := validator.GetCapturedSQLs("GetOrderInfoByOrderCode")

	// 验证捕获的SQL
	assert.GreaterOrEqual(t, len(capturedSQLs), 1, "应该捕获到SQL语句")
}

// TestSQLValidationConfig 测试SQL验证配置
func TestSQLValidationConfig(t *testing.T) {
	config := GetSQLValidationConfig()

	// 测试配置方法
	assert.NotNil(t, config, "配置不应该为空")

	// 测试方法启用/禁用
	config.EnableMethod("TestMethod")
	assert.True(t, config.IsMethodEnabled("TestMethod"), "方法应该被启用")

	config.DisableMethod("TestMethod")
	assert.False(t, config.IsMethodEnabled("TestMethod"), "方法应该被禁用")

	// 测试阈值设置
	config.SetAlertThreshold(10)
	assert.Equal(t, 10, config.GetAlertThreshold(), "阈值应该被正确设置")
}

// TestSQLValidator 测试SQL验证器
func TestSQLValidator(t *testing.T) {
	validator := GetSQLValidator()

	// 测试SQL捕获
	validator.CaptureSQL("TestMethod", "v1", "SELECT * FROM test WHERE id = ?", []interface{}{1}, nil)
	validator.CaptureSQL("TestMethod", "v2", "SELECT * FROM test WHERE id = ?", []interface{}{1}, nil)

	// 测试SQL对比
	diff := validator.CompareAndValidate("TestMethod")
	assert.Nil(t, diff, "相同的SQL不应该有差异")

	// 测试不同的SQL
	validator.ClearCapturedSQL("TestMethod2")
	validator.CaptureSQL("TestMethod2", "v1", "SELECT * FROM test WHERE id = ?", []interface{}{1}, nil)
	validator.CaptureSQL("TestMethod2", "v2", "SELECT * FROM test WHERE name = ?", []interface{}{"test"}, nil)

	diff = validator.CompareAndValidate("TestMethod2")
	assert.NotNil(t, diff, "不同的SQL应该有差异")
	assert.Equal(t, "TestMethod2", diff.Method, "方法名应该匹配")
}

// 辅助函数：设置测试订单
func setupTestOrder(orderCode string, userID uint) uint {
	orderInfo := &OrderInfo{
		OrderCode: orderCode,
		UserID:    userID,
		OrderType: POD_ORDER,
		ShopType:  ORDER_SHOP_TYPE_SHOPIFY,
		CreatedAt: time.Now(),
		UpdatedAt: time.Now(),
		StateInfo: StateInfo{
			OrderState: NOT_PAY,
		},
		PriceInfo: PriceInfo{
			TotalMoney: 2599,
		},
	}

	// 直接使用v2方法创建，避免SQL验证干扰
	err := orderInfo.createV2()
	if err != nil {
		panic("设置测试订单失败: " + err.Error())
	}

	return orderInfo.ID
}

// 辅助函数：清理测试订单
func cleanupTestOrder(orderCode string) {
	// 这里应该实现清理逻辑，删除测试数据
	// 可以使用原生SQL或者直接的数据库操作
	// 避免使用可能触发SQL验证的方法
}
