package order

import (
	"strings"
	"testing"

	"github.com/stretchr/testify/assert"
)

// TestLogStateFieldHandling 测试LogState字段处理
func TestLogStateFieldHandling(t *testing.T) {
	validator := GetSQLValidator()

	// 测试用例1：检查LogState字段是否被正确处理
	t.Run("LogState字段对比", func(t *testing.T) {
		// 模拟GORM v1的SQL（可能不包含LogState字段）
		v1SQL := `INSERT INTO order_info (created_at, updated_at, user_id, order_code, total_money, order_state) VALUES (?, ?, ?, ?, ?, ?)`

		// 模拟GORM v2的SQL（包含LogState字段）
		v2SQL := `INSERT INTO order_info (created_at, updated_at, user_id, order_code, total_money, order_state, log_state) VALUES ('2023-12-01 10:00:00', '2023-12-01 10:00:00', 12345, 'TEST001', 2599, 1, '[{"order_state":1,"state_time":1701417600}]')`

		// 标准化处理
		normalized1 := validator.normalizeSQLForComparison(v1SQL, "v1")
		normalized2 := validator.normalizeSQLForComparison(v2SQL, "v2")

		t.Logf("V1 原始: %s", v1SQL)
		t.Logf("V2 原始: %s", v2SQL)
		t.Logf("V1 标准化: %s", normalized1)
		t.Logf("V2 标准化: %s", normalized2)

		// 提取字段结构
		structure1 := validator.extractInsertStructure(normalized1)
		structure2 := validator.extractInsertStructure(normalized2)

		t.Logf("V1 结构: %s", structure1)
		t.Logf("V2 结构: %s", structure2)

		// 检查是否包含log_state字段
		v1HasLogState := strings.Contains(structure1, "log_state")
		v2HasLogState := strings.Contains(structure2, "log_state")

		t.Logf("V1 包含log_state: %v", v1HasLogState)
		t.Logf("V2 包含log_state: %v", v2HasLogState)

		// 验证SQL等价性
		isEquivalent := validator.isSQLEquivalent(v1SQL, v2SQL)
		t.Logf("SQL等价: %v", isEquivalent)

		// 如果字段结构不同，应该被检测出来
		if v1HasLogState != v2HasLogState {
			assert.False(t, isEquivalent, "字段结构不同时应该被检测出差异")
		}
	})

	// 测试用例2：完整的SQL和参数对比（包含LogState差异）
	t.Run("完整对比包含LogState差异", func(t *testing.T) {
		// 清理之前的捕获记录
		validator.ClearCapturedSQL("TestLogStateField")

		// 模拟GORM v1的SQL和参数（不包含LogState）
		v1SQL := `INSERT INTO order_info (created_at, updated_at, user_id, order_code, total_money, order_state) VALUES (?, ?, ?, ?, ?, ?)`
		v1Args := []interface{}{"2023-12-01 10:00:00", "2023-12-01 10:00:00", 12345, "TEST001", 2599, 1}
		validator.CaptureSQL("TestLogStateField", "v1", v1SQL, v1Args, nil)

		// 模拟GORM v2的SQL和参数（包含LogState）
		v2SQL := `INSERT INTO order_info (created_at, updated_at, user_id, order_code, total_money, order_state, log_state) VALUES ('2023-12-01 10:00:00', '2023-12-01 10:00:00', 12345, 'TEST001', 2599, 1, '[{"order_state":1,"state_time":1701417600}]')`
		v2Args := []interface{}{}
		validator.CaptureSQL("TestLogStateField", "v2", v2SQL, v2Args, nil)

		// 执行对比
		diff := validator.CompareAndValidate("TestLogStateField")

		// 应该检测到差异
		assert.NotNil(t, diff, "LogState字段差异应该被检测出来")

		t.Logf("V1 SQL: %s", v1SQL)
		t.Logf("V1 Args: %v", v1Args)
		t.Logf("V2 SQL: %s", v2SQL)
		t.Logf("V2 Args: %v", v2Args)
		t.Logf("检测到差异: %v", diff != nil)
		if diff != nil {
			t.Logf("差异详情: %+v", diff)
		}
	})

	// 测试用例3：相同LogState字段的情况
	t.Run("相同LogState字段", func(t *testing.T) {
		// 清理之前的捕获记录
		validator.ClearCapturedSQL("TestSameLogState")

		// 模拟GORM v1的SQL和参数（包含LogState）
		v1SQL := `INSERT INTO order_info (created_at, updated_at, user_id, order_code, total_money, order_state, log_state) VALUES (?, ?, ?, ?, ?, ?, ?)`
		v1Args := []interface{}{"2023-12-01 10:00:00", "2023-12-01 10:00:00", 12345, "TEST001", 2599, 1, `[{"order_state":1,"state_time":1701417600}]`}
		validator.CaptureSQL("TestSameLogState", "v1", v1SQL, v1Args, nil)

		// 模拟GORM v2的SQL和参数（包含相同的LogState）
		v2SQL := `INSERT INTO order_info (created_at, updated_at, user_id, order_code, total_money, order_state, log_state) VALUES ('2023-12-01 10:00:00', '2023-12-01 10:00:00', 12345, 'TEST001', 2599, 1, '[{"order_state":1,"state_time":1701417600}]')`
		v2Args := []interface{}{}
		validator.CaptureSQL("TestSameLogState", "v2", v2SQL, v2Args, nil)

		// 执行对比
		diff := validator.CompareAndValidate("TestSameLogState")

		// 应该没有差异
		assert.Nil(t, diff, "相同的LogState字段不应该有差异")

		t.Logf("V1 SQL: %s", v1SQL)
		t.Logf("V1 Args: %v", v1Args)
		t.Logf("V2 SQL: %s", v2SQL)
		t.Logf("V2 Args: %v", v2Args)
		t.Logf("差异: %v", diff)
	})
}

// TestLogStateFieldNormalization 测试LogState字段标准化
func TestLogStateFieldNormalization(t *testing.T) {
	validator := GetSQLValidator()

	testCases := []struct {
		name        string
		sql         string
		shouldHave  bool
		description string
	}{
		{
			name:        "包含log_state字段",
			sql:         "INSERT INTO order_info (user_id, order_code, log_state) VALUES (?, ?, ?)",
			shouldHave:  true,
			description: "SQL包含log_state字段",
		},
		{
			name:        "不包含log_state字段",
			sql:         "INSERT INTO order_info (user_id, order_code) VALUES (?, ?)",
			shouldHave:  false,
			description: "SQL不包含log_state字段",
		},
		{
			name:        "包含log_state字段（大写）",
			sql:         "INSERT INTO order_info (user_id, order_code, LOG_STATE) VALUES (?, ?, ?)",
			shouldHave:  true,
			description: "SQL包含LOG_STATE字段（大写）",
		},
		{
			name:        "包含log_state字段（混合大小写）",
			sql:         "INSERT INTO order_info (user_id, order_code, Log_State) VALUES (?, ?, ?)",
			shouldHave:  true,
			description: "SQL包含Log_State字段（混合大小写）",
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			normalized := validator.normalizeSQL(tc.sql)
			structure := validator.extractInsertStructure(normalized)

			hasLogState := strings.Contains(structure, "log_state")

			t.Logf("原始SQL: %s", tc.sql)
			t.Logf("标准化SQL: %s", normalized)
			t.Logf("结构: %s", structure)
			t.Logf("包含log_state: %v", hasLogState)
			t.Logf("期望包含: %v", tc.shouldHave)

			assert.Equal(t, tc.shouldHave, hasLogState, tc.description)
		})
	}
}

// TestLogStateFieldComparison 测试LogState字段对比逻辑
func TestLogStateFieldComparison(t *testing.T) {
	validator := GetSQLValidator()

	// 测试字段结构对比
	t.Run("字段结构对比", func(t *testing.T) {
		// 不包含log_state的结构
		sql1 := "INSERT INTO order_info (user_id, order_code, total_money) VALUES (?, ?, ?)"
		structure1 := validator.extractInsertStructure(validator.normalizeSQL(sql1))

		// 包含log_state的结构
		sql2 := "INSERT INTO order_info (user_id, order_code, total_money, log_state) VALUES (?, ?, ?, ?)"
		structure2 := validator.extractInsertStructure(validator.normalizeSQL(sql2))

		t.Logf("结构1: %s", structure1)
		t.Logf("结构2: %s", structure2)

		// 结构应该不同
		assert.NotEqual(t, structure1, structure2, "包含不同字段的结构应该不同")

		// 验证字段差异
		hasLogState1 := strings.Contains(structure1, "log_state")
		hasLogState2 := strings.Contains(structure2, "log_state")

		assert.False(t, hasLogState1, "结构1不应该包含log_state")
		assert.True(t, hasLogState2, "结构2应该包含log_state")
	})

	// 测试参数对比
	t.Run("参数对比", func(t *testing.T) {
		// 不包含log_state的参数
		args1 := []interface{}{12345, "TEST001", 2599}

		// 包含log_state的参数
		args2 := []interface{}{12345, "TEST001", 2599, `[{"order_state":1,"state_time":1701417600}]`}

		// 参数数量应该不同
		assert.NotEqual(t, len(args1), len(args2), "参数数量应该不同")

		// 前面的参数应该相同
		for i := 0; i < len(args1); i++ {
			isEquivalent := validator.isArgValueEquivalent(args1[i], args2[i])
			assert.True(t, isEquivalent, "前面的参数应该相同")
		}
	})
}
