package order

import (
	"strings"
	"testing"

	"github.com/stretchr/testify/assert"
)

// TestAssociationFieldsRemoval 测试关联字段移除功能
func TestAssociationFieldsRemoval(t *testing.T) {
	validator := GetSQLValidator()

	// 测试用例1：INSERT语句中的关联字段移除
	t.Run("INSERT关联字段移除", func(t *testing.T) {
		// 模拟GORM v1生成的包含关联字段的SQL
		v1SQL := `INSERT INTO order_info (created_at, updated_at, user_id, order_code, total_money, order_item, track_info) VALUES (?, ?, ?, ?, ?, ?, ?)`
		
		// 模拟GORM v2生成的正确SQL（不包含关联字段）
		v2SQL := `INSERT INTO order_info (created_at, updated_at, user_id, order_code, total_money) VALUES ('2023-12-01 10:00:00', '2023-12-01 10:00:00', 12345, 'TEST001', 2599)`

		// 标准化处理
		normalized1 := validator.normalizeSQLForComparison(v1SQL, "v1")
		normalized2 := validator.normalizeSQLForComparison(v2SQL, "v2")

		t.Logf("V1 原始: %s", v1SQL)
		t.Logf("V2 原始: %s", v2SQL)
		t.Logf("V1 标准化: %s", normalized1)
		t.Logf("V2 标准化: %s", normalized2)

		// 验证关联字段被移除
		assert.NotContains(t, normalized1, "order_item", "V1标准化后不应包含order_item")
		assert.NotContains(t, normalized1, "track_info", "V1标准化后不应包含track_info")

		// 验证SQL等价性
		isEquivalent := validator.isSQLEquivalent(v1SQL, v2SQL)
		assert.True(t, isEquivalent, "移除关联字段后应该被认为是等价的")
	})

	// 测试用例2：参数调整功能
	t.Run("参数调整功能", func(t *testing.T) {
		// 模拟GORM v1的参数（包含关联字段的参数）
		v1Args := []interface{}{"2023-12-01 10:00:00", "2023-12-01 10:00:00", 12345, "TEST001", 2599, "order_item_data", "track_info_data"}
		v1SQL := `INSERT INTO order_info (created_at, updated_at, user_id, order_code, total_money, order_item, track_info) VALUES (?, ?, ?, ?, ?, ?, ?)`

		// 调整参数
		adjustedArgs := validator.adjustArgsForAssociationFields(v1Args, v1SQL)

		t.Logf("原始参数: %v", v1Args)
		t.Logf("调整后参数: %v", adjustedArgs)

		// 验证参数数量被正确调整
		expectedLength := len(v1Args) - 2 // 移除order_item和track_info两个参数
		assert.Equal(t, expectedLength, len(adjustedArgs), "参数数量应该被正确调整")

		// 验证移除的是最后两个参数
		for i := 0; i < expectedLength; i++ {
			assert.Equal(t, v1Args[i], adjustedArgs[i], "前面的参数应该保持不变")
		}
	})

	// 测试用例3：完整的SQL和参数对比
	t.Run("完整SQL和参数对比", func(t *testing.T) {
		// 清理之前的捕获记录
		validator.ClearCapturedSQL("TestAssociationFields")

		// 模拟GORM v1的SQL和参数（包含关联字段）
		v1SQL := `INSERT INTO order_info (created_at, updated_at, user_id, order_code, total_money, order_item, track_info) VALUES (?, ?, ?, ?, ?, ?, ?)`
		v1Args := []interface{}{"2023-12-01 10:00:00", "2023-12-01 10:00:00", 12345, "TEST001", 2599, "order_item_data", "track_info_data"}
		validator.CaptureSQL("TestAssociationFields", "v1", v1SQL, v1Args, nil)

		// 模拟GORM v2的SQL和参数（不包含关联字段）
		v2SQL := `INSERT INTO order_info (created_at, updated_at, user_id, order_code, total_money) VALUES ('2023-12-01 10:00:00', '2023-12-01 10:00:00', 12345, 'TEST001', 2599)`
		v2Args := []interface{}{}
		validator.CaptureSQL("TestAssociationFields", "v2", v2SQL, v2Args, nil)

		// 执行对比
		diff := validator.CompareAndValidate("TestAssociationFields")

		// 应该没有差异
		assert.Nil(t, diff, "处理关联字段后不应该有差异")

		t.Logf("V1 SQL: %s", v1SQL)
		t.Logf("V1 Args: %v", v1Args)
		t.Logf("V2 SQL: %s", v2SQL)
		t.Logf("V2 Args: %v", v2Args)
		t.Logf("差异: %v", diff)
	})

	// 测试用例4：只有一个关联字段的情况
	t.Run("单个关联字段", func(t *testing.T) {
		v1SQL := `INSERT INTO order_info (created_at, user_id, order_code, order_item) VALUES (?, ?, ?, ?)`
		v1Args := []interface{}{"2023-12-01 10:00:00", 12345, "TEST001", "order_item_data"}

		adjustedArgs := validator.adjustArgsForAssociationFields(v1Args, v1SQL)

		// 应该移除一个参数
		assert.Equal(t, len(v1Args)-1, len(adjustedArgs), "应该移除一个参数")
		assert.Equal(t, []interface{}{"2023-12-01 10:00:00", 12345, "TEST001"}, adjustedArgs, "应该移除最后一个参数")
	})

	// 测试用例5：没有关联字段的情况
	t.Run("无关联字段", func(t *testing.T) {
		v1SQL := `INSERT INTO order_info (created_at, user_id, order_code) VALUES (?, ?, ?)`
		v1Args := []interface{}{"2023-12-01 10:00:00", 12345, "TEST001"}

		adjustedArgs := validator.adjustArgsForAssociationFields(v1Args, v1SQL)

		// 参数应该保持不变
		assert.Equal(t, v1Args, adjustedArgs, "没有关联字段时参数应该保持不变")
	})
}

// TestRemoveAssociationFieldsFromInsert 测试INSERT语句关联字段移除
func TestRemoveAssociationFieldsFromInsert(t *testing.T) {
	validator := GetSQLValidator()

	testCases := []struct {
		name     string
		input    string
		expected string
	}{
		{
			name:     "移除order_item字段",
			input:    "INSERT INTO order_info (user_id, order_code, order_item) VALUES (?, ?, ?)",
			expected: "INSERT INTO order_info (user_id, order_code) VALUES (?, ?)",
		},
		{
			name:     "移除track_info字段",
			input:    "INSERT INTO order_info (user_id, order_code, track_info) VALUES (?, ?, ?)",
			expected: "INSERT INTO order_info (user_id, order_code) VALUES (?, ?)",
		},
		{
			name:     "移除两个关联字段",
			input:    "INSERT INTO order_info (user_id, order_code, order_item, track_info) VALUES (?, ?, ?, ?)",
			expected: "INSERT INTO order_info (user_id, order_code) VALUES (?, ?)",
		},
		{
			name:     "没有关联字段",
			input:    "INSERT INTO order_info (user_id, order_code) VALUES (?, ?)",
			expected: "INSERT INTO order_info (user_id, order_code) VALUES (?, ?)",
		},
		{
			name:     "非INSERT语句",
			input:    "SELECT * FROM order_info WHERE order_item IS NOT NULL",
			expected: "SELECT * FROM order_info WHERE order_item IS NOT NULL",
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			result := validator.removeAssociationFieldsFromInsert(tc.input)
			
			t.Logf("输入: %s", tc.input)
			t.Logf("输出: %s", result)
			t.Logf("期望: %s", tc.expected)

			// 验证关联字段被移除
			if strings.Contains(tc.input, "order_item") && strings.Contains(tc.input, "INSERT") {
				assert.NotContains(t, result, "order_item", "order_item字段应该被移除")
			}
			if strings.Contains(tc.input, "track_info") && strings.Contains(tc.input, "INSERT") {
				assert.NotContains(t, result, "track_info", "track_info字段应该被移除")
			}
		})
	}
}

// TestRemoveLastValuesParams 测试VALUES参数移除
func TestRemoveLastValuesParams(t *testing.T) {
	validator := GetSQLValidator()

	testCases := []struct {
		name     string
		input    string
		count    int
		expected string
	}{
		{
			name:     "移除最后1个参数",
			input:    "INSERT INTO test (a, b, c) VALUES (?, ?, ?)",
			count:    1,
			expected: "INSERT INTO test (a, b, c) VALUES (?, ?)",
		},
		{
			name:     "移除最后2个参数",
			input:    "INSERT INTO test (a, b, c, d) VALUES (?, ?, ?, ?)",
			count:    2,
			expected: "INSERT INTO test (a, b, c, d) VALUES (?, ?)",
		},
		{
			name:     "移除0个参数",
			input:    "INSERT INTO test (a, b) VALUES (?, ?)",
			count:    0,
			expected: "INSERT INTO test (a, b) VALUES (?, ?)",
		},
		{
			name:     "移除数量超过参数总数",
			input:    "INSERT INTO test (a) VALUES (?)",
			count:    5,
			expected: "INSERT INTO test (a) VALUES (?)",
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			result := validator.removeLastValuesParams(tc.input, tc.count)
			
			t.Logf("输入: %s", tc.input)
			t.Logf("移除数量: %d", tc.count)
			t.Logf("输出: %s", result)
			t.Logf("期望: %s", tc.expected)

			// 基本验证：结果应该是有效的SQL
			assert.Contains(t, result, "INSERT", "结果应该包含INSERT")
			assert.Contains(t, result, "VALUES", "结果应该包含VALUES")
		})
	}
}
