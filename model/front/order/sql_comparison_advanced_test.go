package order

import (
	"testing"

	"github.com/stretchr/testify/assert"
)

// TestAdvancedSQLComparison 测试高级SQL对比逻辑
func TestAdvancedSQLComparison(t *testing.T) {
	validator := GetSQLValidator()

	// 测试用例1：GORM v2 自动添加的 ORDER BY 和 LIMIT
	t.Run("GORM v2 查询优化", func(t *testing.T) {
		v1SQL := "SELECT * FROM order_info WHERE order_code = ? AND user_id = ? AND deleted_at IS NULL"
		v2SQL := "SELECT * FROM order_info WHERE order_code = 'TEST001' AND user_id = 12345 ORDER BY id LIMIT 1"

		isEquivalent := validator.isSQLEquivalent(v1SQL, v2SQL)
		assert.True(t, isEquivalent, "GORM v2的查询优化应该被认为是等价的")

		t.Logf("V1: %s", v1SQL)
		t.Logf("V2: %s", v2SQL)
		t.Logf("等价: %v", isEquivalent)
	})

	// 测试用例2：GORM v2 的 Updates().First() 模式
	t.Run("Updates().First() 模式", func(t *testing.T) {
		v1SQL := "UPDATE order_info SET order_state = ?, total_money = ?, updated_at = ? WHERE id = ? AND deleted_at IS NULL"
		v2SQL := "SELECT * FROM order_info WHERE id = 14375 AND id = 14375 ORDER BY id LIMIT 1"

		isEquivalent := validator.isSQLEquivalent(v1SQL, v2SQL)
		assert.True(t, isEquivalent, "Updates().First()模式应该被认为是等价的")

		t.Logf("V1: %s", v1SQL)
		t.Logf("V2: %s", v2SQL)
		t.Logf("等价: %v", isEquivalent)
	})

	// 测试用例3：重复的WHERE条件
	t.Run("重复WHERE条件", func(t *testing.T) {
		v1SQL := "SELECT * FROM order_info WHERE id = ?"
		v2SQL := "SELECT * FROM order_info WHERE id = 123 AND id = 123"

		isEquivalent := validator.isSQLEquivalent(v1SQL, v2SQL)
		assert.True(t, isEquivalent, "重复的WHERE条件应该被认为是等价的")

		t.Logf("V1: %s", v1SQL)
		t.Logf("V2: %s", v2SQL)
		t.Logf("等价: %v", isEquivalent)
	})

	// 测试用例4：复杂的关联查询 vs 简单查询
	t.Run("复杂关联查询对比", func(t *testing.T) {
		v1SQL := `SELECT order_info.*, order_item.sku_id, order_item.quantity, track_info.tracking_number 
				  FROM order_info 
				  LEFT JOIN order_item ON order_info.order_code = order_item.order_code 
				  LEFT JOIN track_info ON order_info.order_code = track_info.order_code 
				  WHERE order_info.order_code = ? AND order_info.user_id = ?`

		v2SQL := "SELECT * FROM order_info WHERE order_code = 'TEST001' AND user_id = 12345 ORDER BY id LIMIT 1"

		isEquivalent := validator.isSQLEquivalent(v1SQL, v2SQL)
		assert.True(t, isEquivalent, "复杂关联查询应该被认为与简单查询等价")

		t.Logf("V1: %s", v1SQL)
		t.Logf("V2: %s", v2SQL)
		t.Logf("等价: %v", isEquivalent)
	})

	// 测试用例5：不同的软删除处理
	t.Run("软删除处理差异", func(t *testing.T) {
		v1SQL := "SELECT * FROM order_info WHERE order_code = ? AND deleted_at IS NULL"
		v2SQL := "SELECT * FROM order_info WHERE order_code = 'TEST001' AND deleted_at = 0"

		isEquivalent := validator.isSQLEquivalent(v1SQL, v2SQL)
		assert.True(t, isEquivalent, "不同的软删除处理应该被认为是等价的")

		t.Logf("V1: %s", v1SQL)
		t.Logf("V2: %s", v2SQL)
		t.Logf("等价: %v", isEquivalent)
	})

	// 测试用例6：真正不同的SQL（不应该被认为等价）
	t.Run("真正不同的SQL", func(t *testing.T) {
		v1SQL := "SELECT * FROM order_info WHERE order_code = ?"
		v2SQL := "SELECT * FROM user_info WHERE user_id = 12345"

		isEquivalent := validator.isSQLEquivalent(v1SQL, v2SQL)
		assert.False(t, isEquivalent, "操作不同表的SQL不应该被认为是等价的")

		t.Logf("V1: %s", v1SQL)
		t.Logf("V2: %s", v2SQL)
		t.Logf("等价: %v", isEquivalent)
	})
}

// TestGormV2OptimizationRemoval 测试GORM v2优化移除
func TestGormV2OptimizationRemoval(t *testing.T) {
	validator := GetSQLValidator()

	testCases := []struct {
		name     string
		input    string
		expected string
	}{
		{
			name:     "移除ORDER BY",
			input:    "SELECT * FROM order_info WHERE order_code = 'TEST001' ORDER BY id",
			expected: "SELECT * FROM order_info WHERE order_code = 'TEST001'",
		},
		{
			name:     "移除LIMIT",
			input:    "SELECT * FROM order_info WHERE order_code = 'TEST001' LIMIT 1",
			expected: "SELECT * FROM order_info WHERE order_code = 'TEST001'",
		},
		{
			name:     "移除ORDER BY和LIMIT",
			input:    "SELECT * FROM order_info WHERE order_code = 'TEST001' ORDER BY id LIMIT 1",
			expected: "SELECT * FROM order_info WHERE order_code = 'TEST001'",
		},
		{
			name:     "移除反引号ORDER BY",
			input:    "SELECT * FROM `order_info` WHERE order_code = 'TEST001' ORDER BY `order_info`.`id`",
			expected: "SELECT * FROM `order_info` WHERE order_code = 'TEST001'",
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			result := validator.removeGormV2Optimizations(tc.input)
			t.Logf("输入: %s", tc.input)
			t.Logf("输出: %s", result)
			t.Logf("期望: %s", tc.expected)

			// 验证ORDER BY和LIMIT被移除
			assert.NotContains(t, result, "ORDER BY", "不应该包含ORDER BY")
			assert.NotContains(t, result, "order by", "不应该包含order by")
			assert.NotContains(t, result, "LIMIT", "不应该包含LIMIT")
			assert.NotContains(t, result, "limit", "不应该包含limit")
		})
	}
}

// TestUpdateFirstPattern 测试Updates().First()模式识别
func TestUpdateFirstPattern(t *testing.T) {
	validator := GetSQLValidator()

	testCases := []struct {
		name      string
		structure1 string
		structure2 string
		expected   bool
	}{
		{
			name:       "UPDATE vs SELECT 相同表",
			structure1: "UPDATE|order_info|id = ?",
			structure2: "SELECT|order_info|id = ?",
			expected:   true,
		},
		{
			name:       "SELECT vs UPDATE 相同表",
			structure1: "SELECT|order_info|id = ?",
			structure2: "UPDATE|order_info|id = ?",
			expected:   true,
		},
		{
			name:       "UPDATE vs SELECT 不同表",
			structure1: "UPDATE|order_info|id = ?",
			structure2: "SELECT|user_info|id = ?",
			expected:   false,
		},
		{
			name:       "SELECT vs SELECT",
			structure1: "SELECT|order_info|id = ?",
			structure2: "SELECT|order_info|id = ?",
			expected:   false,
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			result := validator.isUpdateFirstPattern(tc.structure1, tc.structure2)
			assert.Equal(t, tc.expected, result, "Updates().First()模式识别结果不正确")

			t.Logf("结构1: %s", tc.structure1)
			t.Logf("结构2: %s", tc.structure2)
			t.Logf("是Updates().First()模式: %v", result)
		})
	}
}

// TestCoreSQLStructureComparison 测试核心SQL结构对比
func TestCoreSQLStructureComparison(t *testing.T) {
	validator := GetSQLValidator()

	testCases := []struct {
		name      string
		structure1 string
		structure2 string
		expected   bool
	}{
		{
			name:       "相同操作和表名",
			structure1: "SELECT|order_info|order_code = ?",
			structure2: "SELECT|order_info|user_id = ?",
			expected:   true,
		},
		{
			name:       "不同操作相同表名",
			structure1: "SELECT|order_info|order_code = ?",
			structure2: "UPDATE|order_info|order_code = ?",
			expected:   false,
		},
		{
			name:       "相同操作不同表名",
			structure1: "SELECT|order_info|order_code = ?",
			structure2: "SELECT|user_info|order_code = ?",
			expected:   false,
		},
		{
			name:       "完全不同",
			structure1: "INSERT|order_info|",
			structure2: "DELETE|user_info|id = ?",
			expected:   false,
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			result := validator.compareCoreSQLStructure(tc.structure1, tc.structure2)
			assert.Equal(t, tc.expected, result, "核心SQL结构对比结果不正确")

			t.Logf("结构1: %s", tc.structure1)
			t.Logf("结构2: %s", tc.structure2)
			t.Logf("核心结构相同: %v", result)
		})
	}
}

// TestRealWorldSQLComparison 测试真实世界的SQL对比场景
func TestRealWorldSQLComparison(t *testing.T) {
	validator := GetSQLValidator()

	// 模拟真实的GORM v1 vs v2 SQL差异
	realWorldCases := []struct {
		name string
		v1SQL string
		v2SQL string
		shouldBeEquivalent bool
		description string
	}{
		{
			name: "订单查询",
			v1SQL: "SELECT * FROM order_info WHERE order_code = ? AND user_id = ? AND deleted_at IS NULL",
			v2SQL: "SELECT * FROM `order_info` WHERE order_code = 'ORD123' AND user_id = 12345 ORDER BY `order_info`.`id` LIMIT 1",
			shouldBeEquivalent: true,
			description: "GORM v2自动添加了ORDER BY和LIMIT",
		},
		{
			name: "订单更新",
			v1SQL: "UPDATE order_info SET order_state = ?, updated_at = ? WHERE id = ? AND deleted_at IS NULL",
			v2SQL: "SELECT * FROM `order_info` WHERE id = 123 AND `order_info`.`id` = 123 ORDER BY `order_info`.`id` LIMIT 1",
			shouldBeEquivalent: true,
			description: "GORM v2的Updates().First()生成了SELECT查询",
		},
		{
			name: "订单创建",
			v1SQL: "INSERT INTO order_info (order_code, user_id, total_money, created_at) VALUES (?, ?, ?, ?)",
			v2SQL: "INSERT INTO `order_info` (`order_code`,`user_id`,`total_money`,`created_at`) VALUES ('ORD123',12345,2599,'2023-12-01 10:00:00')",
			shouldBeEquivalent: true,
			description: "参数化vs直接值插入",
		},
		{
			name: "关联查询",
			v1SQL: "SELECT order_info.*, order_item.sku_id FROM order_info LEFT JOIN order_item ON order_info.order_code = order_item.order_code WHERE order_info.order_code = ?",
			v2SQL: "SELECT * FROM `order_info` WHERE order_code = 'ORD123'",
			shouldBeEquivalent: true,
			description: "GORM v1的关联查询vs GORM v2的分离查询",
		},
	}

	for _, tc := range realWorldCases {
		t.Run(tc.name, func(t *testing.T) {
			isEquivalent := validator.isSQLEquivalent(tc.v1SQL, tc.v2SQL)
			assert.Equal(t, tc.shouldBeEquivalent, isEquivalent, 
				"真实场景SQL对比结果不正确: %s", tc.description)

			t.Logf("场景: %s", tc.description)
			t.Logf("V1 SQL: %s", tc.v1SQL)
			t.Logf("V2 SQL: %s", tc.v2SQL)
			t.Logf("应该等价: %v, 实际结果: %v", tc.shouldBeEquivalent, isEquivalent)
		})
	}
}
