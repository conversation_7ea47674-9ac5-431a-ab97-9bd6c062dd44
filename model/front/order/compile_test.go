package order

import (
	"testing"
	"time"
	"zx/unit/pkg/mysql"

	"github.com/jinzhu/gorm"
)

func InitMysql() {
	var mysqlCfgTest = mysql.Config{
		Addr:         "mysql-test-20240228.cuhd7h3xijsl.us-east-2.rds.amazonaws.com:3306",
		Username:     "admin",
		Password:     "00056ef19b84a86c03a3c5df81a62c6dbed",
		Database:     "pod_partner",
		ShowSQL:      true,
		MaxIdleInter: 2,
	}
	if err := mysql.Init(&mysqlCfgTest); err != nil {
		panic(">>>>>>>> err: " + err.<PERSON>rror())
	}
}

// TestCompileCheck 编译检查测试
func TestCompileCheck(t *testing.T) {
	// 这个测试主要是为了检查代码是否能正常编译
	// 不执行实际的数据库操作

	InitMysql()

	// 测试结构体创建
	orderInfo := &OrderInfo{
		OrderCode: "TEST-COMPILE-001",
		UserID:    12345,
		OrderType: POD_ORDER,
		ShopType:  ORDER_SHOP_TYPE_SHOPIFY,
		CreatedAt: time.Now(),
		UpdatedAt: time.Now(),
		StateInfo: StateInfo{
			OrderState: NOT_PAY,
		},
		PriceInfo: PriceInfo{
			TotalMoney: 2599,
		},
	}

	// 测试方法签名是否正确
	_ = orderInfo.TableName()

	// 测试SQL验证器创建
	validator := GetSQLValidator()
	if validator == nil {
		t.Error("SQL验证器不应该为nil")
	}

	// 测试配置创建
	config := GetSQLValidationConfig()
	if config == nil {
		t.Error("SQL验证配置不应该为nil")
	}

	// 测试SQL捕获器创建（跳过，因为需要数据库连接）
	// capturer := NewGormV1SQLCapturer("TestMethod")
	// if capturer == nil {
	// 	t.Error("GORM v1 SQL捕获器不应该为nil")
	// }

	// 测试SQL捕获日志器创建
	logger := NewSQLCaptureLogger("TestMethod")
	if logger == nil {
		t.Error("SQL捕获日志器不应该为nil")
	}

	t.Log("编译检查通过")
}

// TestMethodSignatures 测试方法签名
func TestMethodSignatures(t *testing.T) {
	orderInfo := &OrderInfo{
		OrderCode: "TEST-SIGNATURE-001",
		UserID:    12345,
	}

	// 测试方法签名是否保持兼容
	// 这些调用不会实际执行，只是检查编译是否通过

	// 测试Create方法签名
	var createFunc func(...*gorm.DB) error = orderInfo.Create
	if createFunc == nil {
		t.Error("Create方法签名不正确")
	}

	// 测试Update方法签名
	var updateFunc func(...*gorm.DB) error = orderInfo.Update
	if updateFunc == nil {
		t.Error("Update方法签名不正确")
	}

	// 测试QueryByOrderCode方法签名
	var queryFunc func() error = orderInfo.QueryByOrderCode
	if queryFunc == nil {
		t.Error("QueryByOrderCode方法签名不正确")
	}

	// 测试GetOrderInfoByOrderCode方法签名
	var getInfoFunc func(...string) error = orderInfo.GetOrderInfoByOrderCode
	if getInfoFunc == nil {
		t.Error("GetOrderInfoByOrderCode方法签名不正确")
	}

	t.Log("方法签名检查通过")
}

// TestSQLValidatorInterface 测试SQL验证器接口
func TestSQLValidatorInterface(t *testing.T) {
	validator := GetSQLValidator()

	// 测试基本方法
	validator.Enable()
	if !validator.IsEnabled() {
		t.Error("启用SQL验证器失败")
	}

	validator.Disable()
	if validator.IsEnabled() {
		t.Error("禁用SQL验证器失败")
	}

	// 测试SQL捕获
	validator.CaptureSQL("TestMethod", "v1", "SELECT * FROM test", []interface{}{}, nil)
	validator.CaptureSQL("TestMethod", "v2", "SELECT * FROM test", []interface{}{}, nil)

	// 测试获取捕获的SQL
	sqls := validator.GetCapturedSQLs("TestMethod")
	if len(sqls) != 2 {
		t.Errorf("期望捕获2条SQL，实际捕获%d条", len(sqls))
	}

	// 测试清理
	validator.ClearCapturedSQL("TestMethod")
	sqls = validator.GetCapturedSQLs("TestMethod")
	if len(sqls) != 0 {
		t.Errorf("清理后应该没有SQL，实际还有%d条", len(sqls))
	}

	t.Log("SQL验证器接口检查通过")
}

// TestConfigInterface 测试配置接口
func TestConfigInterface(t *testing.T) {
	config := GetSQLValidationConfig()

	// 测试方法启用/禁用
	config.EnableMethod("TestMethod")
	if !config.IsMethodEnabled("TestMethod") {
		t.Error("启用方法失败")
	}

	config.DisableMethod("TestMethod")
	if config.IsMethodEnabled("TestMethod") {
		t.Error("禁用方法失败")
	}

	// 测试阈值设置
	originalThreshold := config.GetAlertThreshold()
	config.SetAlertThreshold(10)
	if config.GetAlertThreshold() != 10 {
		t.Error("设置报警阈值失败")
	}
	config.SetAlertThreshold(originalThreshold) // 恢复原值

	// 测试启用状态
	originalEnabled := config.IsEnabled()
	config.SetEnabled(true)
	if !config.IsEnabled() {
		t.Error("启用配置失败")
	}
	config.SetEnabled(originalEnabled) // 恢复原值

	t.Log("配置接口检查通过")
}

// TestLoggerInterface 测试日志器接口
func TestLoggerInterface(t *testing.T) {
	logger := NewSQLCaptureLogger("TestMethod")

	// 测试接口方法（这些方法不会实际执行，只是检查接口实现）
	_ = logger.LogMode(0)
	logger.Info(nil, "test")
	logger.Warn(nil, "test")
	logger.Error(nil, "test")
	logger.Trace(nil, time.Now(), func() (string, int64) {
		return "SELECT 1", 1
	}, nil)

	t.Log("日志器接口检查通过")
}

// TestManagerInterface 测试管理器接口
func TestManagerInterface(t *testing.T) {
	manager := GetSQLValidationManager()
	if manager == nil {
		t.Error("SQL验证管理器不应该为nil")
	}

	// 测试管理器方法
	report := manager.GetValidationReport()
	if report == nil {
		t.Error("验证报告不应该为nil")
	}

	// 测试方法管理
	manager.EnableMethodValidation("TestMethod")
	manager.DisableMethodValidation("TestMethod")

	t.Log("管理器接口检查通过")
}
