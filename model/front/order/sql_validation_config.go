package order

import (
	"os"
	"strconv"
	"strings"
	"sync"
)

// SQLValidationConfig SQL验证配置
type SQLValidationConfig struct {
	mu                    sync.RWMutex
	enabled               bool
	environment           string
	enabledMethods        map[string]bool
	alertThreshold        int
	logSQLDifferences     bool
	fallbackToV1OnError   bool
	enableDetailedLogging bool
}

var (
	globalConfig *SQLValidationConfig
	configOnce   sync.Once
)

// GetSQLValidationConfig 获取全局SQL验证配置
func GetSQLValidationConfig() *SQLValidationConfig {
	configOnce.Do(func() {
		globalConfig = &SQLValidationConfig{
			enabled:               getEnvBool("SQL_VALIDATION_ENABLED", true),
			environment:           getEnvString("ENVIRONMENT", "development"),
			enabledMethods:        make(map[string]bool),
			alertThreshold:        getEnvInt("SQL_VALIDATION_ALERT_THRESHOLD", 5),
			logSQLDifferences:     getEnvBool("SQL_VALIDATION_LOG_DIFFERENCES", true),
			fallbackToV1OnError:   getEnvBool("SQL_VALIDATION_FALLBACK_V1", false),
			enableDetailedLogging: getEnvBool("SQL_VALIDATION_DETAILED_LOG", false),
		}
		
		// 初始化启用的方法
		globalConfig.initEnabledMethods()
	})
	return globalConfig
}

// IsEnabled 检查SQL验证是否启用
func (c *SQLValidationConfig) IsEnabled() bool {
	c.mu.RLock()
	defer c.mu.RUnlock()
	
	// 生产环境默认禁用
	if c.environment == "production" {
		return false
	}
	
	return c.enabled
}

// IsMethodEnabled 检查特定方法是否启用SQL验证
func (c *SQLValidationConfig) IsMethodEnabled(method string) bool {
	c.mu.RLock()
	defer c.mu.RUnlock()
	
	if !c.IsEnabled() {
		return false
	}
	
	// 如果没有配置特定方法，默认启用
	if len(c.enabledMethods) == 0 {
		return true
	}
	
	return c.enabledMethods[method]
}

// EnableMethod 启用特定方法的SQL验证
func (c *SQLValidationConfig) EnableMethod(method string) {
	c.mu.Lock()
	defer c.mu.Unlock()
	c.enabledMethods[method] = true
}

// DisableMethod 禁用特定方法的SQL验证
func (c *SQLValidationConfig) DisableMethod(method string) {
	c.mu.Lock()
	defer c.mu.Unlock()
	c.enabledMethods[method] = false
}

// GetAlertThreshold 获取报警阈值
func (c *SQLValidationConfig) GetAlertThreshold() int {
	c.mu.RLock()
	defer c.mu.RUnlock()
	return c.alertThreshold
}

// ShouldLogDifferences 是否记录SQL差异
func (c *SQLValidationConfig) ShouldLogDifferences() bool {
	c.mu.RLock()
	defer c.mu.RUnlock()
	return c.logSQLDifferences
}

// ShouldFallbackToV1 是否在错误时回退到V1
func (c *SQLValidationConfig) ShouldFallbackToV1() bool {
	c.mu.RLock()
	defer c.mu.RUnlock()
	return c.fallbackToV1OnError
}

// IsDetailedLoggingEnabled 是否启用详细日志
func (c *SQLValidationConfig) IsDetailedLoggingEnabled() bool {
	c.mu.RLock()
	defer c.mu.RUnlock()
	return c.enableDetailedLogging
}

// SetEnabled 设置是否启用SQL验证
func (c *SQLValidationConfig) SetEnabled(enabled bool) {
	c.mu.Lock()
	defer c.mu.Unlock()
	c.enabled = enabled
}

// SetAlertThreshold 设置报警阈值
func (c *SQLValidationConfig) SetAlertThreshold(threshold int) {
	c.mu.Lock()
	defer c.mu.Unlock()
	c.alertThreshold = threshold
}

// initEnabledMethods 初始化启用的方法列表
func (c *SQLValidationConfig) initEnabledMethods() {
	// 从环境变量读取启用的方法列表
	methodsStr := getEnvString("SQL_VALIDATION_ENABLED_METHODS", "")
	if methodsStr == "" {
		// 默认启用所有核心方法
		c.enabledMethods["Create"] = true
		c.enabledMethods["Update"] = true
		c.enabledMethods["QueryByOrderCode"] = true
		c.enabledMethods["GetOrderInfoByOrderCode"] = true
		c.enabledMethods["QueryByOrderCodes"] = true
		c.enabledMethods["DeleteByIds"] = true
		return
	}
	
	methods := strings.Split(methodsStr, ",")
	for _, method := range methods {
		method = strings.TrimSpace(method)
		if method != "" {
			c.enabledMethods[method] = true
		}
	}
}

// GetEnabledMethods 获取启用的方法列表
func (c *SQLValidationConfig) GetEnabledMethods() []string {
	c.mu.RLock()
	defer c.mu.RUnlock()
	
	methods := make([]string, 0, len(c.enabledMethods))
	for method, enabled := range c.enabledMethods {
		if enabled {
			methods = append(methods, method)
		}
	}
	return methods
}

// 环境变量辅助函数
func getEnvString(key, defaultValue string) string {
	if value := os.Getenv(key); value != "" {
		return value
	}
	return defaultValue
}

func getEnvBool(key string, defaultValue bool) bool {
	if value := os.Getenv(key); value != "" {
		if parsed, err := strconv.ParseBool(value); err == nil {
			return parsed
		}
	}
	return defaultValue
}

func getEnvInt(key string, defaultValue int) int {
	if value := os.Getenv(key); value != "" {
		if parsed, err := strconv.Atoi(value); err == nil {
			return parsed
		}
	}
	return defaultValue
}

// SQLValidationManager SQL验证管理器
type SQLValidationManager struct {
	config    *SQLValidationConfig
	validator *SQLValidator
}

// NewSQLValidationManager 创建SQL验证管理器
func NewSQLValidationManager() *SQLValidationManager {
	return &SQLValidationManager{
		config:    GetSQLValidationConfig(),
		validator: GetSQLValidator(),
	}
}

// StartValidation 开始SQL验证
func (m *SQLValidationManager) StartValidation() {
	if m.config.IsEnabled() {
		m.validator.Enable()
		log.Info("SQL验证已启用", 
			"environment", m.config.environment,
			"enabled_methods", m.config.GetEnabledMethods(),
			"alert_threshold", m.config.GetAlertThreshold())
	} else {
		m.validator.Disable()
		log.Info("SQL验证已禁用", "environment", m.config.environment)
	}
}

// StopValidation 停止SQL验证
func (m *SQLValidationManager) StopValidation() {
	m.validator.Disable()
	log.Info("SQL验证已停止")
}

// GetValidationReport 获取验证报告
func (m *SQLValidationManager) GetValidationReport() map[string]interface{} {
	return map[string]interface{}{
		"enabled":           m.config.IsEnabled(),
		"environment":       m.config.environment,
		"enabled_methods":   m.config.GetEnabledMethods(),
		"alert_threshold":   m.config.GetAlertThreshold(),
		"difference_count":  m.validator.differenceCount,
		"log_differences":   m.config.ShouldLogDifferences(),
		"fallback_to_v1":    m.config.ShouldFallbackToV1(),
		"detailed_logging":  m.config.IsDetailedLoggingEnabled(),
	}
}

// EnableMethodValidation 启用特定方法的验证
func (m *SQLValidationManager) EnableMethodValidation(method string) {
	m.config.EnableMethod(method)
	log.Info("已启用方法SQL验证", "method", method)
}

// DisableMethodValidation 禁用特定方法的验证
func (m *SQLValidationManager) DisableMethodValidation(method string) {
	m.config.DisableMethod(method)
	log.Info("已禁用方法SQL验证", "method", method)
}

// 全局管理器实例
var globalManager *SQLValidationManager

// InitSQLValidation 初始化SQL验证
func InitSQLValidation() {
	globalManager = NewSQLValidationManager()
	globalManager.StartValidation()
}

// GetSQLValidationManager 获取全局SQL验证管理器
func GetSQLValidationManager() *SQLValidationManager {
	if globalManager == nil {
		InitSQLValidation()
	}
	return globalManager
}
