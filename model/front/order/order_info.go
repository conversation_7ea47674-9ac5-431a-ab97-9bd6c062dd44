package order

import (
	"encoding/json"
	"errors"
	"fmt"
	"reflect"
	"time"
	"zx/unit/pkg/ecode"
	"zx/unit/pkg/mysql"
	"zx/zx-consistency/global"
	"zx/zx-consistency/model"
	"zx/zx-consistency/pkg/define"
	"zx/zxgo/log"

	"github.com/jinzhu/gorm"
	gorm2 "gorm.io/gorm"
)

func init() {
	mysql.RegisterTable((*OrderInfo)(nil), mysql.OPTION_USE_INNODB_ENGINE,
		mysql.OPTION_USE_UTF8mb4_CHARSET,
		mysql.OPTION_USE_UTF8mb4_GENERAL_CI_COLLATE)
	model.SyncRegister((*OrderInfo)(nil))

	// 初始化SQL验证
	InitSQLValidation()
}

const LABELNO_SPLIT = ","

const (
	POD_ORDER   = 1 // 平台内样品订单
	SHOP_ORDER  = 2 // 平台内商店订单
	THIRD_ORDER = 3 // 第三方同步订单
	AFTER_ORDER = 4 // 售后（重发）订单| 但是在获取接口时，orderType判断使用 8,只有写入时为4
)

const (
	ORDER_SHOP_TYPE_SHOPIFY = 1
	ORDER_SHOP_TYPE_ETSY    = 2
	ORDER_SHOP_TYPE_WIX     = 3
	ORDER_SHOP_TYPE_WOO     = 4
	ORDER_SHOP_TYPE_S       = 5
)

const (
	_                           = iota
	OrderSpecialistStateChanged = 1 // 订单特殊状态，中转标签在海外仓被扫了
)

type OrderInfo struct {
	ID        uint      `gorm:"PRIMARY_KEY" json:"id"`
	CreatedAt time.Time `json:"created_at"`
	UpdatedAt time.Time `json:"updated_at"`
	UserID    uint      `json:"user_id"`
	Level     string    `json:"level"` // 下单时，用户的会员等级

	OrderType         int    `json:"order_type"`                                //订单类型
	ShopID            uint   `json:"shop_id"`                                   // 商店id 若订单类型为THIRD_ORDER，则记录shopID和ThirdOrderCode
	ShopName          string `json:"shop_name"`                                 //
	ShopNameText      string `json:"shop_name_text"`                            // 店铺名称 显示文本
	OrderCode         string `json:"order_code"  gorm:"unique_index;not null;"` // 平台内订单号
	ThirdOrderCode    string `json:"third_order_code"`                          // 第三方平台订单号 若平台内订单则为空
	WorkOrderState    int    `json:"work_order_state"`                          // 工单状态 1待生成 2生成成功
	ThirdLocationID   int64  `json:"third_location_id"`                         // 第三方平台的 location_id  |shopify用的
	ThirdItemIds      string `json:"third_item_ids" gorm:"type:BLOB;"`          // 第三方平台订单子项的id，一维数组
	FulfillmentID     int64  `json:"fulfillment_id"`                            // 创建的 shopify 履约ID
	WixFulfillmentID  string `json:"wix_fulfillment_id"`                        // 创建的 wix 履约 ID
	ThirdOrderName    string `json:"third_order_name"`                          // 第三方订单名称
	ShopType          int    `json:"shop_type"  gorm:"not null"`                // 1 shopify  2etsy  3 wix
	BindFulfillment   string `json:"bind_fulfillment"`                          // 绑定的变体进行履约
	BindFulfillmentID string `json:"bind_fulfillment_id"`                       // 绑定的变体履约的ID
	AllCustomCount    int    `json:"all_custom_count"  gorm:"default:0"`        // 半自定义图层数量
	DeliveryCar       string `json:"delivery_car"`                              // 发货时分配的发货 车-排-盒

	AfterSaleCode      string `json:"after_sale_code"  gorm:"type:BLOB;"`   // 售后ID
	RetryOrderCode     string `json:"retry_order_code"  gorm:"type:BLOB;"`  // 重发订单号
	Responsible        string `json:"responsible"  gorm:"type:BLOB;"`       // 责任方
	AuditState         int    `json:"audit_state"`                          // 审核状态 1 待审核  2 审核通过  3 审核不通过
	DigitizationStatus int    `json:"digitization_status" gorm:"default:0"` // 制版任务状态 2025年3月18日17:59:55 zc 新增

	// 用于板带审核的,一个订单下存在一个板带任务审核不通过,则订单的审核状态为审核不通过

	OutboundId       int64  `json:"outbound_id" gorm:"default:0"`   // 中转的出库单 id，默认为0（即不需要中转），-1：需要中转，>0：中转出库后对应的出库单id
	SpecialState     uint64 `json:"special_state" gorm:"default:0"` // 订单特殊状态，具体参考 const OrderSpecialistState
	CopyrightRisk    int    `json:"copyright_risk"`                 // 订单版权风控状态  0 1 正常 2风控
	IsManualOverride int    `json:"is_manual_override"`             // 是否人工解除  0否 1是

	DbVersion int64 `json:"db_version" gorm:"default:0"` // 数据版本

	RecipientInfo //收货人信息
	PriceInfo     //价格信息
	Logistics     //物流信息
	StateInfo     //状态信息

	OrderItem []*OrderItem `json:"order_item" gorm:"foreignkey:order_code;association_foreignkey:order_code"`
	TrackInfo *TrackInfo   `json:"track_info"  gorm:"foreignkey:order_code;association_foreignkey:order_code"`
}

func (o *OrderInfo) TableName() string {
	return "order_info"
}

// 新建订单
func (o *OrderInfo) Create(tx ...*gorm.DB) error {
	// SQL对比验证（开发/测试环境）
	if GetSQLValidator().IsEnabled() {
		return o.createWithSQLValidation(tx...)
	}

	// 生产环境直接执行GORM v2
	return o.createV2(tx...)
}

// createWithSQLValidation 带SQL验证的创建方法
func (o *OrderInfo) createWithSQLValidation(tx ...*gorm.DB) error {
	method := "Create"
	validator := GetSQLValidator()

	// 清理之前的捕获记录
	validator.ClearCapturedSQL(method)

	// 1. 捕获GORM v1的SQL（不执行）
	capturer := NewGormV1SQLCapturer(method)
	var txPtr *gorm.DB
	if len(tx) > 0 {
		txPtr = tx[0]
	}
	capturer.CaptureCreateSQL(o, txPtr)

	// 2. 执行GORM v2版本
	err := o.createV2(tx...)

	// 3. 对比SQL差异
	if diff := validator.CompareAndValidate(method); diff != nil {
		log.Error("Create方法SQL差异", "difference", diff)
		// 可以选择回退到v1或继续使用v2结果
	}

	return err
}

// createV2 GORM v2版本的创建方法
func (o *OrderInfo) createV2(tx ...*gorm.DB) error {
	db := mysql.NewConnV2()
	if len(tx) != 0 {
		db = tx[0]
	}

	var logStateList = make([]*LogEventState, 0, 0)
	logStateList = append(logStateList, &LogEventState{
		OrderState: o.OrderState,
		StateTime:  time.Now().Unix(),
	})

	LogListJson, err := json.Marshal(logStateList)
	if err != nil {
		return err
	}
	o.LogState = string(LogListJson)

	// 使用SQL捕获日志器
	if GetSQLValidator().IsEnabled() {
		captureLogger := NewSQLCaptureLogger("Create")
		db = db.Session(&gorm2.Session{Logger: captureLogger})
	}

	return db.Table(o.TableName()).Create(o).Error
}

func (o *OrderInfo) GetOrderInfoByOrderCode(preload ...string) error {
	// SQL对比验证（开发/测试环境）
	if GetSQLValidator().IsEnabled() {
		return o.getOrderInfoByOrderCodeWithSQLValidation(preload...)
	}

	// 生产环境直接执行GORM v2
	return o.getOrderInfoByOrderCodeV2(preload...)
}

// getOrderInfoByOrderCodeWithSQLValidation 带SQL验证的查询方法
func (o *OrderInfo) getOrderInfoByOrderCodeWithSQLValidation(preload ...string) error {
	method := "GetOrderInfoByOrderCode"
	validator := GetSQLValidator()

	// 清理之前的捕获记录
	validator.ClearCapturedSQL(method)

	// 1. 捕获GORM v1的SQL（不执行）
	capturer := NewGormV1SQLCapturer(method)
	capturer.CapturePreloadSQL(o, preload)

	// 2. 执行GORM v2版本
	err := o.getOrderInfoByOrderCodeV2(preload...)

	// 3. 对比SQL差异
	if diff := validator.CompareAndValidate(method); diff != nil {
		log.Error("GetOrderInfoByOrderCode方法SQL差异", "difference", diff)
	}

	return err
}

// getOrderInfoByOrderCodeV2 GORM v2版本的查询方法
func (o *OrderInfo) getOrderInfoByOrderCodeV2(preload ...string) error {
	db := mysql.NewConnV2().Model(o).Where("order_code = ?", o.OrderCode)

	db = db.Preload("OrderItem").Preload("OrderItem.SkuInfo")

	if len(preload) > 0 {
		for _, v := range preload {
			db = db.Preload(v)
		}
	}

	if o.UserID > 0 {
		db = db.Where("user_id = ?", o.UserID)
	}

	// 使用SQL捕获日志器
	if GetSQLValidator().IsEnabled() {
		captureLogger := NewSQLCaptureLogger("GetOrderInfoByOrderCode")
		db = db.Session(&gorm2.Session{Logger: captureLogger})
	}

	return db.First(o).Error
}

func (o *OrderInfo) GetOrderInfoByOrderCodeAndUserID() error {
	return mysql.NewConn().Model(o).Where("order_code = ?", o.OrderCode).Where("user_id = ?", o.UserID).First(o).Error
}

type CountInfo struct {
	NOT_PAYCount              int `json:"not_pay_count"`
	HAVE_PAYCount             int `json:"have_pay_count"`
	HAVE_LOGISTICSCount       int `json:"have_logistics_count"`
	PROBLEM_STATECount        int `json:"problem_state_count"`
	IN_PRODUCTINGCount        int `json:"in_producting_count"`
	PRODUCTING_OKCount        int `json:"producting_ok_count"`
	TESTING_OKCount           int `json:"testing_ok_count"`
	CANCELEDCount             int `json:"canceled_count"`
	COMPLETEDCount            int `json:"completed_count"`
	GOODSOUTOFSYNCCount       int `json:"goods_out_of_sync_count"`
	UNDELIVERABLEADDRESSCount int `json:"undeliverable_address_count"`
	RefundCount               int `json:"refund_count"`
	DELIVEREDCount            int `json:"delivered_count"`
	CustomCount               int `json:"custom_count"` //半自定义且未付款

	AllCount int `json:"all_count"` //全部订单数量
	Count    int `json:"count"`
}

type OrderListInfo struct {
	ID              uint      `json:"-"`
	OrderType       int       `json:"order_type"` //订单类型
	ShopID          uint      `json:"shop_id"`    //商店id 若订单类型为SHOP_ORDER，则记录shopID和ThirdOrderCode
	UserID          uint      `json:"user_id"`
	ShopName        string    `json:"shop_name"`               //商店名称
	ShopNameText    string    `json:"shop_name_text"`          // 店铺名称 显示文本
	OrderCode       string    `json:"order_code"`              //平台内订单号
	FirstName       string    `json:"first_name"`              //名/姓名
	CreatedAt       time.Time `json:"-"`                       //创建时间
	CreatedAtInt    int64     `json:"created_at"`              //创建时间
	PayTime         int64     `json:"pay_time"`                //支付时间
	TotalMoney      int32     `json:"total_money"`             //订单总金额
	CommodityPrices int32     `json:"commodity_prices"`        //商品总价格
	OrderState      int       `json:"order_state"`             //订单状态
	OrderPayState   int       `json:"order_pay_state"`         // 支付状态，目前只用于paypal，0：未捕获，1：approve，2：pending，3：capture
	Country         string    `json:"country"`                 //国家名称
	RefNo           string    `json:"ref_no"  gorm:"size:512"` //运单号码
	Email           string    `json:"email" sensitive:"true"`  // 用户邮箱
	Responsible     string    `json:"responsible"`             // 责任方

	ProblemRemark  string `json:"problem_remark"`                     //问题订单备注 (有就显示)
	WorkOrderState int    `json:"work_order_state"`                   //工单状态 1待生成 2生成成功
	AllCustomCount int    `json:"all_custom_count"  gorm:"default:0"` //半自定义图层数量 0代表没有  范围 0-3
	LatestStatus   string `json:"latest_status"`                      // 物流最新状态

	ShopType   int     `json:"shop_type"`   // 1 shopify  2 etsy
	Currency   string  `json:"currency"`    // 币种
	Rate       float64 `json:"rate"`        // 汇率
	RemoteSign bool    `json:"remote_sign"` // 偏远地区标识 true:偏远地区 false:非偏远地区

	Carrier              string `json:"carrier"`                // 运输方
	MyLogisticsChannel   string `json:"my_logistics_channel"`   // 我们平台的物流渠道
	Payment              int    `json:"-"`                      //支付方式
	PwpDiscountMoney     int    `json:"pwp_discount_money"`     // p卡折扣金额  单位美分 p卡余额实际支付金额为total_money - pwp_discount_money
	AllDigitizationCount int    `json:"all_digitization_count"` // 包含的图片绣框数量，未制版完成的

	TaxPrice       int32 `json:"tax_price"`       // 税费
	LogisticsPrice int32 `json:"logistics_price"` // 物流支付价格
	CopyrightRisk  int   `json:"copyright_risk"`  // 订单版权风控状态  0 1 正常 2风控

}

// 筛选获取订单列表，和各状态数量
func (o *OrderInfo) GetList(pn, ps int, timeS, timeE int64) (list []*OrderListInfo, countInfo CountInfo, err error) {

	db := mysql.NewConn().Table(o.TableName()).Select(`
		order_info.id,
		order_info.order_type,
		order_info.shop_id,
		order_info.user_id,
		order_info.shop_name,
		order_info.shop_name_text,
		order_info.order_code,
		order_info.first_name,
		order_info.created_at,
		order_info.pay_time,
		order_info.total_money,
		order_info.commodity_prices,
		order_info.order_state,
		order_info.order_pay_state,
		order_info.country,
		order_info.ref_no,
		order_info.email,
		order_info.responsible,
		order_info.problem_remark,
		order_info.work_order_state,
		order_info.all_custom_count,
		order_info.latest_status,
		order_info.shop_type,
		order_info.remote_sign,
		order_info.pwp_discount_money,
		order_info.payment,
		SUM(order_item.digitization_count) AS all_digitization_count
	`).
		Joins("LEFT JOIN order_item ON order_info.order_code = order_item.order_code").
		Where("order_info.user_id = ?", o.UserID).
		Where("order_info.order_type != ?", AFTER_ORDER).
		Group("order_info.id")

	if o.OrderState != 0 {
		if o.OrderState == 34 {
			db = db.Where("order_info.order_state = 3 OR order_info.order_state = 4")
		} else if o.OrderState == 66 { //筛选半自定义订单
			db = db.Where("order_info.all_custom_count > ?", 0).Where("order_info.order_state = ?", NOT_PAY)
		} else if o.OrderState == NOT_PAY {
			db = db.Where("order_info.all_custom_count = ?", 0).Where("order_info.order_state = ?", NOT_PAY)
		} else {
			db = db.Where("order_info.order_state = ?", o.OrderState)
		}
	}

	if timeS != 0 {
		db = db.Where("order_info.created_at >= FROM_UNIXTIME(?)", timeS)
	}
	if timeE != 0 {
		db = db.Where("order_info.created_at <= FROM_UNIXTIME(?)", timeE)
	}
	if o.OrderCode != "" {
		db = db.Where("order_info.order_code LIKE ? OR order_info.third_order_name LIKE ?", "%"+o.OrderCode+"%", "%"+o.OrderCode+"%")
	}
	if o.ShopID != 0 {
		db = db.Where("order_info.shop_id = ?", o.ShopID)
	}
	// 引导 mysql 去走索引
	if o.UserID > 0 {
		db = db.Where("order_info.user_id = ?", o.UserID)
	}

	err = db.Count(&countInfo.Count).Error
	if err != nil {
		return
	}

	err = db.Order("order_info.id desc").Offset((pn - 1) * ps).Limit(ps).Find(&list).Error

	return
}

// 筛选赔偿订单
func (o *OrderInfo) GetListToCompensation(deliveredTimeS, deliveredTimeE int64, payTimeS, payTimeE int64) (list []*OrderInfo, err error) {

	db := mysql.NewConn().Table(o.TableName())

	db = db.Where("order_state >= ? and order_state <= ?", HAVE_WAYBILL, COMPLETED)

	if deliveredTimeS > 0 {
		db = db.Where("delivered_time >= ?", deliveredTimeS)
	}

	if deliveredTimeE > 0 {
		db = db.Where("delivered_time < ?", deliveredTimeE)
	}

	if payTimeS > 0 {
		db = db.Where("pay_time >= ?", payTimeS)
	}

	if payTimeE > 0 {
		db = db.Where("pay_time < ?", payTimeE)
	}

	if o.UserID > 0 {
		db = db.Where("user_id = ?", o.UserID)
	}

	err = db.Find(&list).Error

	return
}

// 筛选赔偿订单
func (o *OrderInfo) GetPaiedList() (list []*OrderInfo, err error) {

	db := mysql.NewConn().Table(o.TableName())

	db = db.Where("pay_time > 0 AND order_state <= ?", COMPLETED)

	if o.UserID > 0 {
		db = db.Where("user_id = ?", o.UserID)
	}

	err = db.Find(&list).Error

	return
}

func (o *OrderInfo) GetOrderCompleteMatch(timeS, timeE int64) (list []*OrderListInfo, err error) {

	if o.OrderCode == "" {
		return
	}

	db := mysql.NewConn().Table(o.TableName()).Select(`
		order_info.id,
		order_info.order_type,
		order_info.shop_id,
		order_info.user_id,
		order_info.shop_name,
		order_info.shop_name_text,
		order_info.order_code,
		order_info.first_name,
		order_info.created_at,
		order_info.pay_time,
		order_info.total_money,
		order_info.commodity_prices,
		order_info.order_state,
		order_info.order_pay_state,
		order_info.country,
		order_info.ref_no,
		order_info.email,
		order_info.responsible,
		order_info.problem_remark,
		order_info.work_order_state,
		order_info.all_custom_count,
		order_info.latest_status,
		order_info.shop_type,
		order_info.remote_sign,
		order_info.pwp_discount_money,
		order_info.payment,
		SUM(order_item.digitization_count) AS all_digitization_count
	`).
		Joins("LEFT JOIN order_item ON order_info.order_code = order_item.order_code").
		Where("order_info.user_id = ?", o.UserID).
		Where("order_info.order_type != ?", 4).
		Group("order_info.id")

	if o.OrderState != 0 {
		if o.OrderState == 34 {
			db = db.Where("order_info.order_state = 3 OR order_info.order_state = 4")
		} else if o.OrderState == 66 { // Filter semi-custom orders
			db = db.Where("order_info.all_custom_count > ?", 0).Where("order_info.order_state = ?", NOT_PAY)
		} else if o.OrderState == NOT_PAY {
			db = db.Where("order_info.all_custom_count = ?", 0).Where("order_info.order_state = ?", NOT_PAY)
		} else {
			db = db.Where("order_info.order_state = ?", o.OrderState)
		}
	}

	if timeS != 0 {
		db = db.Where("order_info.created_at >= FROM_UNIXTIME(?)", timeS)
	}
	if timeE != 0 {
		db = db.Where("order_info.created_at <= FROM_UNIXTIME(?)", timeE)
	}

	//if o.OrderCode != "" {
	db = db.Where("order_info.order_code LIKE ? OR order_info.third_order_name LIKE ?", "%"+o.OrderCode+"%", "%"+o.OrderCode+"%")
	//}
	if o.ShopID != 0 {
		db = db.Where("order_info.shop_id = ?", o.ShopID)
	}

	err = db.Find(&list).Error

	return
}

// 获取工厂本地和 pod 端进行校验的数据，只有 id 和 更新时间
func (o *OrderInfo) GetVerifyHead(pn, ps int, idMin, idMax uint, updateTimeS, updateTimeE int64) (list []*model.VerifyHead, count int64, err error) {

	db := mysql.NewConn().Table(o.TableName()).Select("id, UNIX_TIMESTAMP(updated_at) as updated_time")

	if idMax > 0 {
		// 工厂本地查本地 mysql 的校验 head 时 id 的值会大于0
		db = db.Where("id <= ?", idMax)
	}

	if idMin > 0 {
		// 工厂本地查本地 mysql 的校验 head 时 id 的值会大于0
		db = db.Where("id >= ?", idMin)
	}

	if updateTimeS > 0 {
		db = db.Where("updated_at >= FROM_UNIXTIME(?)", updateTimeS)
	}
	if updateTimeE > 0 {
		db = db.Where("updated_at <= FROM_UNIXTIME(?)", updateTimeE)
	}

	err = db.Count(&count).Error
	if err != nil {
		return
	}

	if idMax < 1 && idMin < 1 {
		db = db.Offset((pn - 1) * ps).Limit(ps)
	}

	err = db.Find(&list).Error

	return
}

func (o *OrderInfo) CreateOrUpdate(detail interface{}, idMapType map[uint]int) (err error) {
	detailData, err := json.Marshal(detail)
	if err != nil {
		log.Error(err)
		return
	}

	detailObj := new(OrderInfo)
	err = json.Unmarshal(detailData, detailObj)
	if err != nil {
		log.Error(err)
		err = ecode.WithCode(err, define.JSON_ERR, define.CodeMsgName)
		return
	}

	switch idMapType[detailObj.ID] {
	case 1:
		err = detailObj.Create()
		if err != nil {
			log.Error(err)
			err = ecode.WithCode(err, define.DB_ACCESS_ERROR, define.CodeMsgName)
			return
		}
	case 2:
		err = detailObj.UpdateWithZero()
		if err != nil {
			log.Error(err)
			err = ecode.WithCode(err, define.DB_ACCESS_ERROR, define.CodeMsgName)
			return
		}
	}
	return

}

// 根据 id 获取订单信息
func (o *OrderInfo) GetOrderInfos(ids []uint) (list []*OrderInfo, err error) {

	db := mysql.NewConn().Table(o.TableName())

	db = db.Where("id IN (?)", ids)

	err = db.Find(&list).Error

	return
}

// 根据 id 获取订单信息
func (o *OrderInfo) GetTotalMoneyUserIdByOrderCode() (err error) {

	db := mysql.NewConn().Table(o.TableName())

	db = db.Select("order_code,total_money,user_id").Where("order_code = ?", o.OrderCode)

	err = db.First(o).Error

	return
}

// 根据 id 获取订单信息
func (o *OrderInfo) GetOrderInfosByOrderCodes(orderCode []string) (list []*OrderListInfo, err error) {

	db := mysql.NewConn().Table(o.TableName())

	db = db.Where("order_code IN (?)", orderCode)

	err = db.Find(&list).Error

	return
}

type OrderInfoWithFields struct {
	RealityWeight string `json:"reality_weight"`                            // 真实克重
	OrderCode     string `json:"order_code"  gorm:"unique_index;not null;"` //平台内订单号
}

// 根据 id 获取订单信息
func (o *OrderInfo) GetOrderInfosByOrderCodesWithFields(orderCode []string) (list []*OrderInfoWithFields, err error) {

	fields := global.GetJSONFieldNames(OrderInfoWithFields{}, "")

	db := mysql.NewConn().Table(o.TableName())

	db = db.Where("order_code IN (?)", orderCode)

	if len(fields) < 1 {
		return nil, errors.New("没有查询字段")
	}

	// 查询字段
	db = db.Select(fields)

	err = db.Find(&list).Error

	return
}

// 导出订单 excel 数据
func (o *OrderInfo) GetExportList(payTimeS, payTimeE int64, carriers []string, exportType int, payment []int, orderStates []int) (list []*OrderInfo, countInfo CountInfo, err error) {

	db := mysql.NewConn().Table(o.TableName()) /*.Where("order_type != 4")*/

	switch exportType {
	case 0:
		if len(orderStates) < 1 {
			break
		}
		if (len(orderStates) == 1) && (orderStates[0] == 0) {
			break
		}
		orderStateSql := ""
		for _, oneOrderState := range orderStates {
			if len(orderStateSql) < 1 {
				orderStateSql = fmt.Sprintf("order_state = %d", oneOrderState)
				continue
			}
			orderStateSql = fmt.Sprintf("%s OR order_state = %d", orderStateSql, oneOrderState)
		}
		if len(orderStateSql) > 1 {
			db = db.Where(orderStateSql)
		}
		//db = db.Where("order_state = ? OR order_state = ?", HAVE_WAYBILL, COMPLETED)
	case 1:
		if len(orderStates) < 1 {
			break
		}
		if (len(orderStates) == 1) && (orderStates[0] == 0) {
			break
		}
		if len(orderStates) > 0 {
			db = db.Where("order_state in (?)", orderStates)
		} else if len(orderStates) == 1 { //订单状态单选
			db = db.Where("order_state = ?", o.OrderState)
		}
		break
	default:
		db = db.Where("order_state = ? OR order_state = ?", HAVE_WAYBILL, COMPLETED)
	}

	paymentSql := ""
	if len(payment) > 0 {
		if !((len(payment) == 1) && (payment[0] == 0)) {
			for _, onePayment := range payment {
				if len(paymentSql) < 1 {
					switch onePayment {
					case 1:
						paymentSql = "LENGTH(transaction_order) > 0" // paypal
					case 2:
						paymentSql = "LENGTH(ocean_payment_id) > 0" // 钱海
					case 4:
						paymentSql = "LENGTH(transaction_order) < 1 AND LENGTH(ocean_payment_id) < 1" // 钱包
					default:
						paymentSql = "LENGTH(transaction_order) > 0" // 默认 paypal
					}
					continue
				}

				switch onePayment {
				case 1:
					paymentSql = fmt.Sprintf("%s OR LENGTH(transaction_order) > 0", paymentSql) // paypal
				case 2:
					paymentSql = fmt.Sprintf("%s OR LENGTH(ocean_payment_id) > 0", paymentSql) // 钱海
				case 4:
					paymentSql = fmt.Sprintf("%s OR (LENGTH(transaction_order) < 1 AND LENGTH(ocean_payment_id) < 1)", paymentSql) // 钱包
				default:
					paymentSql = fmt.Sprintf("%s OR LENGTH(transaction_order) > 0", paymentSql) // 默认 paypal
				}
			}
		}
	}

	db = db.Where(paymentSql)

	if payTimeS != 0 {
		db = db.Where("pay_time >= ?", payTimeS)
	}
	if payTimeE != 0 {
		db = db.Where("pay_time <= ?", payTimeE)
	}

	if len(o.CountryCode) > 0 {
		countryList := make([]string, 0, 0)
		err = json.Unmarshal([]byte(o.CountryCode), &countryList)
		if err == nil {
			db = db.Where("country_code IN (?)", countryList)
		} else {
			log.Error(err)
		}
	}

	if len(carriers) > 0 {
		db = db.Where("carrier IN (?)", carriers)
	}

	//db = db.Preload("OrderItem", func(db *gorm.DB) *gorm.DB {
	//	return db.Where("is_pod_sku = ? AND is_ignore = ?", true, false)
	//})

	err = db.Count(&countInfo.Count).Error
	if err != nil {
		return
	}

	err = db.Order("id desc").Find(&list).Error

	return
}

// 导出订单 excel 数据的新方法
func (o *OrderInfo) QueryExportOrders(payTimeS, payTimeE int64, matchType int, orderNumbers []string) (list []*OrderInfo, count int, err error) {
	db := mysql.NewConn().Table(o.TableName())

	// 处理支付时间范围
	if payTimeS != 0 {
		db = db.Where("pay_time >= ?", payTimeS)
	}
	if payTimeE != 0 {
		db = db.Where("pay_time <= ?", payTimeE)
	}

	// 处理匹配类型
	if matchType == 1 {
		// 按订单号匹配
		if len(orderNumbers) > 0 {
			db = db.Where("order_code IN (?)", orderNumbers)
		}
	} else if matchType == 2 {
		// 按运单号匹配
		if len(orderNumbers) > 0 {
			refNoConditions := "1 = 0" // 初始化为 false 条件
			for _, orderNumber := range orderNumbers {
				refNoConditions += fmt.Sprintf(" OR ref_no LIKE '%%%s%%'", orderNumber)
			}
			db = db.Where(refNoConditions)
		}
	} else {
		return nil, 0, fmt.Errorf("无效的匹配类型")
	}

	// 获取订单数量
	err = db.Count(&count).Error
	if err != nil {
		return
	}

	// 获取订单列表
	err = db.Find(&list).Error

	return
}

// 单纯获取订单数量筛选
func (o *OrderInfo) GetCount(timeS, timeE int64) (countInfo CountInfo, err error) {

	db := mysql.NewConn().Table(o.TableName()).Where("user_id = ?", o.UserID).Where("order_type != 4")

	if timeS != 0 {
		db = db.Where("created_at >= FROM_UNIXTIME(?)", timeS)
	}
	if timeE != 0 {
		db = db.Where("created_at <= FROM_UNIXTIME(?)", timeE)
	}
	if o.OrderCode != "" {
		db = db.Where("order_code LIKE ?  OR third_order_name LIKE ?", "%"+o.OrderCode+"%", "%"+o.OrderCode+"%")
	}
	if o.ShopID != 0 {
		db = db.Where("shop_id = ?", o.ShopID)
	}

	//全部订单数量
	err = db.Count(&countInfo.AllCount).Error
	if err != nil {
		return
	}
	//待忽略订单
	err = db.Where("order_state = ?", GOODS_OUT_OF_SYNC).Count(&countInfo.GOODSOUTOFSYNCCount).Error
	if err != nil {
		return
	}
	//地址无法送达订单
	err = db.Where("order_state = ?", UNDELIVERABLE_ADDRESS).Count(&countInfo.UNDELIVERABLEADDRESSCount).Error
	if err != nil {
		return
	}
	//未支付数量
	err = db.Where("order_state = ?", NOT_PAY).Where("all_custom_count = ?", 0).Count(&countInfo.NOT_PAYCount).Error
	if err != nil {
		return
	}
	//已支付数量
	err = db.Where("order_state = ?", HAVE_PAY).Count(&countInfo.HAVE_PAYCount).Error
	if err != nil {
		return
	}
	//已发货数量
	err = db.Where("order_state = ?", HAVE_WAYBILL).Count(&countInfo.HAVE_LOGISTICSCount).Error
	if err != nil {
		return
	}
	//问题订单数量
	err = db.Where("order_state = ?", PROBLEM_STATE).Count(&countInfo.PROBLEM_STATECount).Error
	if err != nil {
		return
	}
	//正在生产数量
	err = db.Where("order_state = ? OR order_state = ?", IN_PRODUCTING, HAVE_WORK).Count(&countInfo.IN_PRODUCTINGCount).Error
	if err != nil {
		return
	}
	//生产完成数量
	err = db.Where("order_state = ?", PRODUCTING_OK).Count(&countInfo.PRODUCTING_OKCount).Error
	if err != nil {
		return
	}
	//质检合格数量
	err = db.Where("order_state = ?", TESTING_OK).Count(&countInfo.TESTING_OKCount).Error
	if err != nil {
		return
	}
	//已取消数量
	err = db.Where("order_state = ?", CANCELED).Count(&countInfo.CANCELEDCount).Error
	if err != nil {
		return
	}
	//已完成数量
	err = db.Where("order_state = ?", COMPLETED).Count(&countInfo.COMPLETEDCount).Error
	if err != nil {
		return
	}
	//退款订单数量
	err = db.Where("order_state = ?", REFUNDED).Count(&countInfo.RefundCount).Error
	if err != nil {
		return
	}
	//已签收订单数量
	err = db.Where("order_state = ?", DELIVERED).Count(&countInfo.DELIVEREDCount).Error
	if err != nil {
		return
	}
	//半自定义且未付款数量
	err = db.Where("order_state = ?", NOT_PAY).Where("all_custom_count > ?", 0).Count(&countInfo.CustomCount).Error
	if err != nil {
		return
	}

	return
}

func (o *OrderInfo) PutOrder(tx *gorm.DB) error {
	return tx.Model(&OrderInfo{}).Where("order_code = ?", o.OrderCode).Where("user_id = ?", o.UserID).Updates(o).First(o).Error
}

func (o *OrderInfo) Update(tx ...*gorm.DB) error {
	// SQL对比验证（开发/测试环境）
	if GetSQLValidator().IsEnabled() {
		return o.updateWithSQLValidation(tx...)
	}

	// 生产环境直接执行GORM v2
	return o.updateV2(tx...)
}

// updateWithSQLValidation 带SQL验证的更新方法
func (o *OrderInfo) updateWithSQLValidation(tx ...*gorm.DB) error {
	method := "Update"
	validator := GetSQLValidator()

	// 清理之前的捕获记录
	validator.ClearCapturedSQL(method)

	// 1. 捕获GORM v1的SQL（不执行）
	capturer := NewGormV1SQLCapturer(method)
	conditions := map[string]interface{}{
		"id": o.ID,
	}
	updates := map[string]interface{}{
		// 这里需要根据实际更新的字段来构建
		"order_state": o.OrderState,
		"total_money": o.TotalMoney,
		// 可以添加更多字段
	}
	capturer.CaptureUpdateSQL(o, conditions, updates)

	// 2. 执行GORM v2版本
	err := o.updateV2(tx...)

	// 3. 对比SQL差异
	if diff := validator.CompareAndValidate(method); diff != nil {
		log.Error("Update方法SQL差异", "difference", diff)
	}

	return err
}

// updateV2 GORM v2版本的更新方法
func (o *OrderInfo) updateV2(tx ...*gorm.DB) error {
	db := mysql.NewConnV2()
	if len(tx) != 0 {
		db = tx[0]
	}

	// 使用SQL捕获日志器
	if GetSQLValidator().IsEnabled() {
		captureLogger := NewSQLCaptureLogger("Update")
		db = db.Session(&gorm2.Session{Logger: captureLogger})
	}

	return db.Model(&OrderInfo{}).Where("id = ?", o.ID).Updates(o).First(o).Error
}

func (o *OrderInfo) UpdateMapByIds(ids []uint, update map[string]interface{}, tx ...*gorm.DB) error {
	if len(ids) == 0 {
		return nil
	}
	db := mysql.NewConn()
	if len(tx) != 0 {
		db = tx[0]
	}

	db = db.Model(&OrderInfo{}).Where("id in (?)", ids)

	return db.Updates(update).Error
}

func (o *OrderInfo) UpdateMapByOrderCodes(orderCodes []string, update map[string]interface{}, tx ...*gorm.DB) error {
	db := mysql.NewConn()
	if len(tx) != 0 {
		db = tx[0]
	}
	if o.Payment != 0 {
		db = db.Where("payment = ?", o.Payment)
	}
	if o.OrderState != 0 {
		db = db.Where("order_state = ?", o.OrderState)
	}
	return db.Model(&OrderInfo{}).Where("order_code in (?)", orderCodes).Updates(update).Error
}

func (o *OrderInfo) UpdateMapMoneyOrders(moneyOrders []string, update map[string]interface{}, tx ...*gorm.DB) error {
	db := mysql.NewConn()
	if len(tx) != 0 {
		db = tx[0]
	}
	return db.Model(&OrderInfo{}).Where("money_order in (?)", moneyOrders).Updates(update).Error
}

// 批量更新
func (o *OrderInfo) UpdateWithOrderCodes(orderCodes []string, tx ...*gorm.DB) error {
	db := mysql.NewConn()
	if len(tx) != 0 {
		db = tx[0]
	}
	return db.Model(&OrderInfo{}).Where("order_code IN (?)", orderCodes).Updates(o).Error
}

// ！！！工厂本地专用，因为工厂本地的数据要喝 pod 后端保持一致，不需要更新时间为当前时间
func (o *OrderInfo) UpdateWithZero(tx ...*gorm2.DB) (err error) {
	db := mysql.NewConnV2()
	if len(tx) != 0 {
		db = tx[0]
	}

	db = db.Table(o.TableName())

	// ！！！这里，这里的更新不能使用 Model，不然的话，
	err = db.Session(&gorm2.Session{DisableNestedTransaction: true}).Select("*").Omit("updated_at").Where("id = ?", o.ID).Updates(o).Error
	if err != nil {
		return
	}

	err = db.Where("id = ?", o.ID).Update("updated_at", o.UpdatedAt).Error

	return
}

func (o *OrderInfo) GetDetailList(ids []uint) (interface{}, error) {

	db := mysql.NewConn().Table(o.TableName())

	db = db.Where("id IN (?)", ids)

	list := make([]*OrderInfo, 0)

	err := db.Find(&list).Error

	return list, err
}

func (o *OrderInfo) DeleteByIds(ids []uint, tx ...*gorm.DB) (err error) {
	db := mysql.NewConn()
	if len(tx) != 0 {
		db = tx[0]
	}
	err = db.Model(&OrderInfo{}).Where("id IN (?)", ids).Delete(o).Error
	return
}

func (o *OrderInfo) DeleteOrder(tx ...*gorm.DB) error {
	db := mysql.NewConn()
	if len(tx) != 0 {
		db = tx[0]
	}

	return db.Table(o.TableName()).Where("order_code = ?", o.OrderCode).Delete(o).Error
}

// 分配和回收发货车，既记录 delivery_car 字段，同时也更新 log_state 字段中正在生产中的
func (o *OrderInfo) UpdateDeliveryCar(tx ...*gorm.DB) (err error) {
	db := mysql.NewConn()
	if len(tx) != 0 {
		db = tx[0]
	}

	if len(o.OrderCode) < 1 {
		return
	}

	db = db.Model(&OrderInfo{}).Where("order_code = ?", o.OrderCode)

	updateMap := map[string]interface{}{
		"delivery_car": o.DeliveryCar,
		//"log_state":    o.LogState,
	}

	if len(o.LogState) > 0 {
		updateMap["log_state"] = o.LogState
	}

	if !o.UpdatedAt.IsZero() {
		updateMap["updated_at"] = o.UpdatedAt
	}

	err = db.Updates(updateMap).Error

	return
}

func (o *OrderInfo) UpdateByOrderCode(tx ...*gorm.DB) error {
	db := mysql.NewConn()
	if len(tx) != 0 {
		db = tx[0]
	}
	return db.Model(&OrderInfo{}).Where("order_code = ?", o.OrderCode).Updates(o).First(o).Error
}

func (o *OrderInfo) UpdateWithVersion(updateAt int64, tx ...*gorm.DB) (rowsAffected int64, err error) {
	db := mysql.NewConn()
	if len(tx) != 0 {
		db = tx[0]
	}

	db = db.Model(&OrderInfo{}).Where("order_code = ?", o.OrderCode)

	// 校验更新时间
	if updateAt > 0 {
		db = db.Where("unix_timestamp(updated_at) = ?", updateAt)
	}
	// 更新数据
	db = db.Updates(o)

	// 查一下更新数据是否有报错
	err = db.Error
	if err != nil {
		// 如果有报错的话直接返回
		return
	}

	// 如果没有报错，记录影响的行数
	rowsAffected = db.RowsAffected

	return
}

func (o *OrderInfo) UpdateByOrderCodeAndUserId(tx ...*gorm.DB) error {
	db := mysql.NewConn()
	if len(tx) != 0 {
		db = tx[0]
	}
	db = db.Model(&OrderInfo{})
	if o.UserID > 0 {
		db = db.Where("user_id = ?", o.UserID)
	}
	return db.Where("order_code = ?", o.OrderCode).Updates(o).Error
}

func (o *OrderInfo) OrderWorkOk(tx *gorm.DB) error {
	return tx.Model(&OrderInfo{}).Where("order_code = ?", o.OrderCode).Where("user_id = ?", o.UserID).Updates(map[string]interface{}{
		"work_order_state":    2,
		"audit_state":         2,
		"digitization_status": o.DigitizationStatus,
		"copyright_risk":      o.CopyrightRisk,
	}).Error
}

func (o *OrderInfo) GetAllDetail() error {
	return mysql.NewConn().Model(o).Where("order_code = ?", o.OrderCode).
		Preload("OrderItem").
		Preload("OrderItem.SkuInfo").
		Preload("OrderItem.SkuInfo.ProductSize").
		Preload("OrderItem.SkuInfo.ProductInfo").
		Preload("OrderItem.SkuInfo.ProductInfo.Supply").
		First(o).Error
}

// 获取所有已发货的订单
func (o *OrderInfo) GetDeliveryOrder() (list []*OrderInfo, err error) {
	db := mysql.NewConn().Table(o.TableName()).Where("order_type != 4")
	if o.UserID > 0 {
		db = db.Where("user_id = ?", o.UserID)
	}
	err = db.Where("order_state = ? OR order_state = ? OR order_state = ?", HAVE_WAYBILL, DELIVERED, DELIVERED).Where("order_code LIKE 'T%'").Find(&list).Error
	//err = db.Where("order_state >= ? AND order_state <= ?", HAVE_PAY, DELIVERED).Where("pay_time >= ?", 1673971200).Where("order_code LIKE 'T%'").Find(&list).Error
	return
}

func (o *OrderInfo) GetWorkItemDetail() error {
	return mysql.NewConn().Model(o).Where("order_code = ?", o.OrderCode).
		Preload("OrderItem", func(db *gorm.DB) *gorm.DB {
			return db.Where("is_pod_sku = ? AND is_ignore = ?", true, false)
		}).
		Preload("OrderItem.SkuInfo").
		Preload("OrderItem.SkuInfo.ProductSize").
		Preload("OrderItem.SkuInfo.ProductInfo").
		Preload("OrderItem.TagDesignInfo").Preload("OrderItem.TagDesignInfo.HangTagInfo").
		Preload("OrderItem.PackDesignInfo").Preload("OrderItem.PackDesignInfo.PackagingInfo").
		First(o).Error
}

func (o *OrderInfo) QueryAll() (list []*OrderInfo, err error) {

	db := mysql.NewConn().Model(o).Where("order_type != 4")
	if o.ShopID > 0 {
		db = db.Where("shop_id = ?", o.ShopID)
	}
	// 引导 mysql 去走索引
	if o.UserID > 0 {
		db = db.Where("user_id = ?", o.UserID)
	}
	err = db.Find(&list).Error
	return
}

func (o *OrderInfo) QueryAllByPayTime(sTime, eTime int64) (list []*OrderInfo, err error) {

	db := mysql.NewConn().Model(o)
	if o.ShopID > 0 {
		db = db.Where("shop_id = ?", o.ShopID)
	}
	if o.UserID > 0 {
		db = db.Where("user_id = ?", o.UserID)
	}

	if o.CreatedAt.Unix() > 0 {
		db = db.Where("created_at >= FROM_UNIXTIME(?)", o.CreatedAt.Unix())
	}

	if sTime > 0 {
		db = db.Where("pay_time >= ?", sTime)
	}

	if eTime > 0 {
		db = db.Where("pay_time <= ?", eTime)
	}

	// 引导 mysql 去走索引
	if o.UserID > 0 {
		db = db.Where("user_id = ?", o.UserID)
	}

	err = db.Find(&list).Error
	return
}

func (o *OrderInfo) QueryAllByCreateTime(sTime, eTime int64) (list []*OrderInfo, err error) {

	db := mysql.NewConn().Model(o)
	if o.ShopID > 0 {
		db = db.Where("shop_id = ?", o.ShopID)
	}
	if o.UserID > 0 {
		db = db.Where("user_id = ?", o.UserID)
	}

	if sTime > 0 {
		db = db.Where("created_at >= FROM_UNIXTIME(?)", sTime)
	}

	if o.OrderState > 0 {
		db = db.Where("order_state = ?", o.OrderState)
	}

	if eTime > 0 {
		db = db.Where("created_at <= FROM_UNIXTIME(?)", eTime)
	}

	err = db.Find(&list).Error
	return
}

func (o *OrderInfo) QueryNotWaybill(payTimeS, payTimeE int64, refNoLen int, isSyncOrder bool) (list []*OrderInfo, err error) {

	db := mysql.NewConn().Table(o.TableName())

	db = db.Where("LENGTH(ref_no) < ?", refNoLen).Where("work_order_state = ?", 2)

	// 筛选出 已支付 且 未贴运单 的订单
	db = db.Where("order_state >= ? AND order_state <= ?", HAVE_PAY, TESTING_OK)

	// 支付时间
	if payTimeS > 0 {
		db = db.Where("pay_time >= ?", payTimeS)
	}
	if payTimeE > 0 {
		db = db.Where("pay_time <= ?", payTimeE)
	}

	// 用户id
	if o.UserID > 0 {
		db = db.Where("user_id = ?", o.UserID)
	}

	if isSyncOrder {
		db = db.Where("order_code LIKE 'T%'")
	}

	err = db.Find(&list).Error
	return
}

func (o *OrderInfo) QueryList() (list []*OrderInfo, err error) {

	db := mysql.NewConn().Table(o.TableName())

	if len(o.RefNo) > 0 {
		db = db.Where("ref_no = ?", o.RefNo)
	}

	// 用户id
	if o.UserID > 0 {
		db = db.Where("user_id = ?", o.UserID)
	}

	if o.OrderState > 0 {
		db = db.Where("order_state = ?", o.OrderState)
	}

	if len(o.MyLogisticsChannel) > 0 {
		db = db.Where("my_logistics_channel = ?", o.MyLogisticsChannel)
	}

	err = db.Find(&list).Error
	return
}

func (o *OrderInfo) QueryToUpdateShopifyOrderList() (list []*OrderInfo, err error) {

	db := mysql.NewConn().Table(o.TableName())

	if len(o.RefNo) > 0 {
		db = db.Where("ref_no = ?", o.RefNo)
	}

	// 用户id
	if o.UserID > 0 {
		db = db.Where("user_id = ?", o.UserID)
	}

	// ShopType 大于 0 的话就根据 shop_type 来查询
	if o.ShopType > 0 {
		db = db.Where("shop_type = ?", o.ShopType)
	}

	db = db.Where("order_type = ?", THIRD_ORDER)

	// 订单状态介于【未支付】和【已发货】之间的同步订单
	db = db.Where("order_state >= ? AND order_state < ? AND order_code LIKE 'T%'", NOT_PAY, HAVE_WAYBILL)

	err = db.Find(&list).Error
	return
}

func (o *OrderInfo) GetNotReleaseDeliveryBox() (list []*OrderInfo, err error) {

	db := mysql.NewConn().Table(o.TableName())

	orderStates := make([]int, 0)
	orderStates = append(orderStates, HAVE_PAY)
	orderStates = append(orderStates, HAVE_WORK)
	orderStates = append(orderStates, IN_PRODUCTING)
	orderStates = append(orderStates, PRODUCTING_OK)
	orderStates = append(orderStates, TESTING_OK)
	//orderStates = append(orderStates, HAVE_WAYBILL)
	//orderStates = append(orderStates, HAVE_LOGISTICS)
	//orderStates = append(orderStates, DELIVERED)
	//orderStates = append(orderStates, COMPLETED)

	db = db.Where("order_state IN (?)", orderStates)

	if len(o.DeliveryCar) > 0 {
		db = db.Where("delivery_car = ?", o.DeliveryCar)
	}

	err = db.Find(&list).Error
	return
}

// 根据 map 来查询列表
func (o *OrderInfo) QueryListByMap(condition []map[string]interface{}) (list []*OrderInfo, err error) {

	db := mysql.NewConn().Table(o.TableName())

	for _, oneCondition := range condition {
		sqlStr := ""
		index := 0
		for k, v := range oneCondition {
			if index > 0 {
				if reflect.TypeOf(v).Kind() == reflect.Slice {
					sqlStr = fmt.Sprintf("%s OR %s IN (%v)", sqlStr, k, v)
				} else {
					sqlStr = fmt.Sprintf("%s OR %s in (%v)", sqlStr, k, v)
				}
			} else {
				if reflect.TypeOf(v).Kind() == reflect.Slice {
					sqlStr = fmt.Sprintf("%s IN (%v)", k, v)
				} else {
					sqlStr = fmt.Sprintf("%s IN (%v)", k, v)
				}
			}
			index++
		}
		db = db.Where(sqlStr)
	}

	err = db.Find(&list).Error

	return
}

// 根据 map 来查询一个数据
func (o *OrderInfo) QueryByMap() (err error) {

	return
}

func (o *OrderInfo) QueryListByRefNoList(refNoList []string) (list []*OrderInfo, err error) {

	db := mysql.NewConn().Table(o.TableName())

	db = db.Where("ref_no IN (?)", refNoList)

	err = db.Find(&list).Error
	return
}

func (o *OrderInfo) QueryListByRefNoListWithUnpack(refNoList []string) (list []*OrderInfo, err error) {

	db := mysql.NewConn().Table(o.TableName())

	sqlStr := ""
	for _, oneRefNo := range refNoList {
		if len(sqlStr) < 1 {
			sqlStr = fmt.Sprintf("ref_no LIKE '%s%%' OR ref_no LIKE '%%·_·%s%%' ", oneRefNo, oneRefNo)
			continue
		}
		sqlStr = fmt.Sprintf("%s OR ref_no LIKE '%s%%' OR ref_no LIKE '%%·_·%s%%' ", sqlStr, oneRefNo, oneRefNo)
	}

	// 添加拆包的标识，引导去走索引
	sqlStr = fmt.Sprintf("(%s) AND oda_result_sign = \"unpack\"", sqlStr)

	db = db.Where(sqlStr)

	err = db.Find(&list).Error
	return
}

// 从客单号进行出库功能的查询，目前是云途 T01 渠道使用
func (o *OrderInfo) QueryListByCustomOrderWith(labelNo string) (list []*OrderInfo, err error) {

	db := mysql.NewConn().Table(o.TableName())

	db = db.Where("logistics_order_id = ?", labelNo)

	err = db.Find(&list).Error
	return
}

// 从客单号进行出库功能的查询（拆包），目前是云途 T01 渠道使用
func (o *OrderInfo) QueryListByCustomOrderWithUnpack(labelNo string) (list []*OrderInfo, err error) {

	db := mysql.NewConn().Table(o.TableName())

	db = db.Where(fmt.Sprintf("logistics_order_id LIKE '%s%%' OR logistics_order_id LIKE '%%,%s%%' ", labelNo, labelNo))

	// 添加拆包的标识，引导去走索引
	db = db.Where("oda_result_sign = \"unpack\"")

	err = db.Find(&list).Error
	return
}

// 查一下 EQ（拆包） 的标签号
func (o *OrderInfo) QueryListByLabelNoWithUnpack(labelNo string) (list []*OrderInfo, err error) {

	db := mysql.NewConn().Table(o.TableName())

	db = db.Where(fmt.Sprintf("label_no LIKE '%s%%' OR label_no LIKE '%%,%s%%' ", labelNo, labelNo))

	err = db.Find(&list).Error
	return
}

func (o *OrderInfo) QueryToMockDelivery(payTimeS, payTimeE int64, userIDs []uint, isSyncOrder bool) (list []*OrderInfo, err error) {

	db := mysql.NewConn().Model(o)

	// 筛选出 已支付 且 未贴运单 的订单，且工单已经生成的
	db = db.Where("order_state >= ? AND order_state <= ?", HAVE_PAY, TESTING_OK).Where("work_order_state = ?", 2)

	// 支付时间
	if payTimeS > 0 {
		db = db.Where("pay_time >= ?", payTimeS)
	}
	if payTimeE > 0 {
		db = db.Where("pay_time <= ?", payTimeE)
	}

	if isSyncOrder {
		db = db.Where("order_code LIKE 'T%'")
	}

	// 用户id
	//if o.UserID > 0 {
	//	db = db.Where("user_id = ?", o.UserID)
	//}
	userIDSql := ""
	for _, oneUserID := range userIDs {
		if len(userIDSql) > 0 {
			userIDSql = fmt.Sprintf("%s OR user_id = %d", userIDSql, oneUserID)
		} else {
			userIDSql = fmt.Sprintf("user_id = %d", oneUserID)
		}
	}
	if len(userIDSql) > 0 {
		db = db.Where(userIDSql)
	}

	err = db.Find(&list).Error

	return
}

// 查找并更新错误订单， 订单状态=已支付（收到 paypal 的授权回调），支付状态=已授权，并且更新时间和当前时间相差大于一小时
// 已弃用
func (o *OrderInfo) UpdateFailedOrders() (err error) {
	db := mysql.NewConn().Model(o)

	// 订单状态=已支付
	db = db.Where("order_state = ?", HAVE_PAY)
	// 订单支付状态=已授权
	db = db.Where("order_pay_state = ?", PayStateApproved)
	// 更新时间和当前时间差两个小时
	db = db.Where("updated_at <= FROM_UNIXTIME(?)", time.Now().Add(time.Hour*-6).Unix())

	err = db.Updates(map[string]interface{}{
		"order_pay_state": PayStateFailed,
		"updated_at":      time.Now(),
	}).Error
	return
}

func (o *OrderInfo) QueryAllNotDelivery() (list []*OrderInfo, err error) {

	db := mysql.NewConn().Model(o)
	// 已支付 到 质检合格
	db = db.Where("order_state >= ?", 2).Where("order_state <= ?", 6)
	// 发往美国
	db = db.Where("country_code = ?", o.CountryCode)
	// 渠道为非 YTW 的
	db = db.Where("carrier = ?", o.Carrier)
	err = db.Find(&list).Error
	return
}

func (o *OrderInfo) QueryToShippedPaypal() (list []*OrderInfo, count int, err error) {
	db := mysql.NewConn().Model(o).Where("order_state = ? OR order_state = ?", HAVE_WAYBILL, COMPLETED).
		Where("payment = ? OR payment = ?", define.PAYPAL, define.VISA)
	err = db.Count(&count).Error
	if err != nil {
		return
	}

	err = db.Order("id desc"). /*.Offset(10).Limit(10)*/ Find(&list).Error

	return
}

func (o *OrderInfo) QueryPaidAll() (list []*OrderInfo, err error) {

	db := mysql.NewConn().Model(o).Where("order_type != 4")
	if o.ShopID > 0 {
		db = db.Where("shop_id = ?", o.ShopID)
	}
	if o.UserID > 0 {
		db = db.Where("user_id = ?", o.UserID)
	}
	// 支付时间大于0，并且状态不是退款
	db = db.Where("pay_time > 0").Where("order_state != ?", REFUNDED)

	err = db.Find(&list).Error
	return
}

func (o *OrderInfo) QueryPaypal() error {

	db := mysql.NewConn().Model(o).Where("order_type != 4")
	if len(o.MoneyOrder) > 0 {
		db = db.Where("money_order = ?", o.MoneyOrder)
	}
	if len(o.TransactionOrder) > 0 {
		db = db.Where("transaction_order = ?", o.TransactionOrder)
	}
	if len(o.OrderCode) > 0 {
		db = db.Where("order_code = ?", o.OrderCode)
	}
	if o.Payment != 0 {
		db = db.Where("payment = ?", o.Payment)
	}

	return db.First(o).Error
}

func (o *OrderInfo) QueryPaypalWithPreload(param ...interface{}) error {

	db := mysql.NewConn().Model(o).Where("order_type != 4")
	if len(o.MoneyOrder) > 0 {
		db = db.Where("money_order = ?", o.MoneyOrder)
	}
	if len(o.TransactionOrder) > 0 {
		db = db.Where("transaction_order = ?", o.TransactionOrder)
	}
	if len(o.OrderCode) > 0 {
		db = db.Where("order_code = ?", o.OrderCode)
	}
	if o.Payment != 0 {
		db = db.Where("payment = ?", o.Payment)
	}

	for _, one := range param {
		db = db.Preload(one.(string))
	}

	return db.First(o).Error
}

func (o *OrderInfo) QueryPaypalList() (list []*OrderInfo, err error) {

	db := mysql.NewConn().Model(o).Where("order_type != 4")
	if o.Payment != 0 {
		db = db.Where("payment = ?", o.Payment)
	}
	if len(o.MoneyOrder) > 0 {
		db = db.Where("money_order = ?", o.MoneyOrder)
	}
	if len(o.TransactionOrder) > 0 {
		db = db.Where("transaction_order = ?", o.TransactionOrder)
	}
	if len(o.OrderCode) > 0 {
		db = db.Where("order_code = ?", o.OrderCode)
	}

	err = db.Order("id desc").Find(&list).Error
	return
}

func (o *OrderInfo) QueryPaypalListBatch() (list []*OrderInfo, err error) {

	if len(o.MoneyOrderBatch) < 1 && len(o.TransactionOrderBatch) < 1 && len(o.OrderCode) < 1 {
		return nil, ecode.New(ecode.CODE_PARAMS_INVALID, "Params are all empty.")
	}

	db := mysql.NewConn().Model(o)
	if len(o.MoneyOrderBatch) > 0 {
		db = db.Where("money_order_batch LIKE ?", "%"+o.MoneyOrderBatch+"%")
	}
	if len(o.TransactionOrderBatch) > 0 {
		db = db.Where("transaction_order_batch LIKE ?", "%"+o.TransactionOrderBatch+"%")
	}
	if len(o.OrderCode) > 0 {
		db = db.Where("order_code = ?", o.OrderCode)
	}

	err = db.Order("id desc").Find(&list).Error
	return
}

type NotWork struct {
	OrderCode string `json:"order_code"` //平台内订单号
}

// 获取已支付未生成工单的订单
func (o *OrderInfo) GetNotWorkOrder() (list []*NotWork, err error) {

	db := mysql.NewConn().Table(o.TableName()).Where("order_state = ?", HAVE_PAY).Where("work_order_state = ?", 1).
		Where("order_pay_state = ? OR order_pay_state = ?", PayStateCompleted, PayStateDefault)

	// 引导 mysql 去走索引
	if o.UserID > 0 {
		db = db.Where("user_id = ?", o.UserID)
	}

	err = db.Find(&list).Error

	return
}

func (o *OrderInfo) GetNoPayOrder() (list []*NotWork, err error) {
	err = mysql.NewConn().Table(o.TableName()).Where("user_id = ?", o.UserID).
		Where("order_state = ?", NOT_PAY).Find(&list).Error
	return
}

/*func (o *OrderInfo) PutOrderState(tx ...*gorm.DB) error {
	db := mysql.NewConn()
	if len(tx) != 0 {
		db = tx[0]
	}
	return db.Table(o.TableName()).Where("order_code = ?", o.OrderCode).Updates(o).Error
}*/

// 修改订单状态
func (o *OrderInfo) PutOrderStateAndLogState(state int, remark string, tx ...*gorm.DB) error {
	var logStateList []*LogEventState

	db := mysql.NewConn()
	if len(tx) != 0 {
		db = tx[0]
	}

	// 改地址时，由有税费国家改到无税费国家，强制更新税费为0
	if o.TaxPrice == -1 {
		err := db.Model(&OrderInfo{}).Where("order_code = ?", o.OrderCode).Updates(map[string]interface{}{
			"tax_price": 0,
		}).Error
		if err != nil {
			return err
		}
		o.TaxPrice = 0
	}

	err := db.Model(&OrderInfo{}).Where("order_code = ?", o.OrderCode).Updates(o).First(o).Error
	if err != nil {
		return err
	}

	if o.OrderState == state {
		return nil
	}
	//已经打印工单的再次打印，则不更新状态(在生产流程中，进度只进不退)
	if (state == 3 || state == 4 || state == 5 || state == 6 || state == 7) && o.OrderState >= state {
		return nil
	}

	err = json.Unmarshal([]byte(o.LogState), &logStateList)
	if err != nil {
		return err
	}

	logStateList = append(logStateList, &LogEventState{
		OrderState: state,
		StateTime:  time.Now().Unix(),
		Remark:     remark,
	})
	LogListJson, err := json.Marshal(logStateList)
	if err != nil {
		return err
	}
	//记录订单的上一个状态
	o.LastOrderState = o.OrderState
	o.LogState = string(LogListJson)
	o.OrderState = state
	//o.UpdatedAt = time.Now()

	db = db.Model(&OrderInfo{}).Where("order_code = ?", o.OrderCode)

	err = db.Updates(o).First(o).Error
	if err != nil {
		return err
	}

	return nil
}

// 修改订单状态，这里的 version 暂时使用订单的更新时间
func (o *OrderInfo) PutOrderStateAndLogStateWithVersion(state int, remark string, version int64, tx ...*gorm.DB) (err error) {
	var logStateList []*LogEventState

	dbInit := mysql.NewConn()
	if len(tx) != 0 {
		dbInit = tx[0]
	}

	db := dbInit

	// 改地址时，由有税费国家改到无税费国家，强制更新税费为0
	if o.TaxPrice == -1 {
		db = db.Model(&OrderInfo{}).Where("order_code = ?", o.OrderCode)
		err := db.Updates(map[string]interface{}{
			"tax_price": 0,
		}).Error
		if err != nil {
			return err
		}
		o.TaxPrice = 0
	}

	db = db.Model(&OrderInfo{}).Where("order_code = ?", o.OrderCode)
	if version > 0 {
		db = db.Where("unix_timestamp(updated_at) = ?", version)
	}
	db = db.Updates(o)
	rowAffected := db.RowsAffected
	if rowAffected < 1 {
		err = errors.New(define.CodeMsg[define.ORDER_REFUND_VERSION_ERR])
		return
	}
	err = dbInit.Where("order_code = ?", o.OrderCode).First(o).Error
	if err != nil {
		return err
	}

	if o.OrderState == state {
		return nil
	}
	//已经打印工单的再次打印，则不更新状态(在生产流程中，进度只进不退)
	if (state == 3 || state == 4 || state == 5 || state == 6 || state == 7) && o.OrderState >= state {
		return nil
	}

	err = json.Unmarshal([]byte(o.LogState), &logStateList)
	if err != nil {
		return err
	}

	logStateList = append(logStateList, &LogEventState{
		OrderState: state,
		StateTime:  time.Now().Unix(),
		Remark:     remark,
	})
	LogListJson, err := json.Marshal(logStateList)
	if err != nil {
		return err
	}
	//记录订单的上一个状态
	o.LastOrderState = o.OrderState
	o.LogState = string(LogListJson)
	o.OrderState = state
	o.UpdatedAt = time.Now()

	db = dbInit.Model(&OrderInfo{}).Where("order_code = ?", o.OrderCode)
	//if version > 0 {
	//	db = db.Where("unix_timestamp(updated_at) = ?", o.UpdatedAt.Unix())
	//}
	db = db.Updates(o)
	rowAffected = db.RowsAffected
	if rowAffected < 1 {
		err = errors.New(define.CodeMsg[define.ORDER_REFUND_VERSION_ERR])
		return
	}
	err = db.First(o).Error
	if err != nil {
		return err
	}

	return nil
}

// 假发货，修改订单状态
func (o *OrderInfo) MockPutOrderStateAndLogState(state int, remark string, mockTime int64, tx ...*gorm.DB) error {
	var logStateList []*LogEventState

	db := mysql.NewConn()
	if len(tx) != 0 {
		db = tx[0]
	}

	if o.TaxPrice == -1 {
		err := db.Model(&OrderInfo{}).Where("order_code = ?", o.OrderCode).Updates(map[string]interface{}{
			"tax_price": 0,
		}).Error
		if err != nil {
			return err
		}
		o.TaxPrice = 0
	}

	err := db.Model(&OrderInfo{}).Where("order_code = ?", o.OrderCode).Updates(o).First(o).Error
	if err != nil {
		return err
	}

	if o.OrderState == state {
		return nil
	}
	//已经打印工单的再次打印，则不更新状态(在生产流程中，进度只进不退)
	if (state == 3 || state == 4 || state == 5 || state == 6 || state == 7) && o.OrderState >= state {
		return nil
	}

	err = json.Unmarshal([]byte(o.LogState), &logStateList)
	if err != nil {
		return err
	}

	if mockTime < 1 {
		mockTime = time.Now().Unix()
	}
	logStateList = append(logStateList, &LogEventState{
		OrderState: state,
		StateTime:  mockTime,
		Remark:     remark,
	})
	LogListJson, err := json.Marshal(logStateList)
	if err != nil {
		return err
	}
	//记录订单的上一个状态
	o.LastOrderState = o.OrderState
	o.LogState = string(LogListJson)
	o.OrderState = state
	o.UpdatedAt = time.Now()

	err = db.Model(&OrderInfo{}).Where("order_code = ?", o.OrderCode).Updates(o).First(o).Error
	if err != nil {
		return err
	}

	return nil
}

// 后台管理获取订单列表的方法
func (o *OrderInfo) AdminGetList(pn, ps, ctimeS, ctimeE, payS, payE int, userIdArr []uint, logisticsChannel string, orderStateList []string, minTotalMoney, maxTotalMoney int) (list []*OrderListInfo, count int, err error) {

	db := mysql.NewConn().Table(o.TableName()).Where("order_type != 4")

	if o.CopyrightRisk != 0 {
		db = db.Where("copyright_risk = ?", o.CopyrightRisk)
	}

	if o.UserID != 0 {
		db = db.Where("user_id = ?", o.UserID)
	}

	if o.MyLogisticsChannel != "" {
		db = db.Where("my_logistics_channel = ?", o.MyLogisticsChannel).Where("order_state >= 2").Where("order_state < 7")
	}

	if logisticsChannel != "" {
		db = db.Where("my_logistics_channel = ?", logisticsChannel)
	}
	/*	if o.MyLogisticsChannel != "" {
		db = db.Where("my_logistics_channel = ?", o.MyLogisticsChannel)
	}*/

	if o.OrderCode != "" {
		db = db.Where("order_code LIKE ?", "%"+o.OrderCode+"%")
	}

	if o.ThirdOrderCode != "" {
		db = db.Where("third_order_code like ? or third_order_name like ?", "%"+o.ThirdOrderCode+"%", "%"+o.ThirdOrderCode+"%")
	}

	if minTotalMoney > 0 && maxTotalMoney > 0 {
		db = db.Where("total_money >= ? and total_money <= ?", minTotalMoney, maxTotalMoney)
	} else if minTotalMoney > 0 && maxTotalMoney == 0 {
		db = db.Where("total_money >= ?", minTotalMoney)
	} else if maxTotalMoney > 0 && minTotalMoney == 0 {
		db = db.Where("total_money <= ?", maxTotalMoney)
	}

	//订单状态多选
	if len(orderStateList) > 0 {
		db = db.Where("order_state in (?)", orderStateList)
	} else if o.OrderState != 0 { //订单状态单选
		db = db.Where("order_state = ?", o.OrderState)
	}

	if o.OrderType != 0 {
		db = db.Where("order_type = ?", o.OrderType)
	}

	if ctimeS != 0 {
		db = db.Where("created_at >= FROM_UNIXTIME(?)", ctimeS)
	}
	if ctimeE != 0 {
		db = db.Where("created_at <= FROM_UNIXTIME(?)", ctimeE)
	}

	if payS != 0 {
		db = db.Where("pay_time >= ?", payS)
	}
	if payE != 0 {
		db = db.Where("pay_time <= ?", payE)
	}

	if o.WorkOrderState != 0 {
		db = db.Where("work_order_state = ?", o.WorkOrderState)
	}

	if len(userIdArr) != 0 {
		db = db.Where("user_id IN (?)", userIdArr)
	}

	if len(o.CountryCode) > 0 {
		db = db.Where("country_code = ?", o.CountryCode)
	}

	if len(o.RefNo) > 0 {
		db = db.Where("ref_no LIKE ? OR old_ref_no LIKE ?", "%"+o.RefNo+"%", "%"+o.RefNo+"%")
	}

	if len(o.TransactionOrder) > 0 {
		db = db.Where("transaction_order LIKE ?", "%"+o.TransactionOrder+"%")
	}
	if len(o.FirstName) > 0 {
		db = db.Where("first_name like ?", "%"+o.FirstName+"%")
	}
	err = db.Count(&count).Error
	if err != nil {
		return
	}

	err = db.Order("id desc").Offset((pn - 1) * ps).Limit(ps).Find(&list).Error

	return
}

// 后台管理获取重发订单列表的方法
func (o *OrderInfo) AdminGetRetryList(pn, ps, ctimeS, ctimeE, payS, payE int, userIdArr []uint, logisticsChannel string, orderStateList []string) (list []*OrderListInfo, count int, err error) {

	db := mysql.NewConn().Table(o.TableName()).Where("order_type = 4")

	if o.UserID != 0 {
		db = db.Where("user_id = ?", o.UserID)
	}

	if o.MyLogisticsChannel != "" {
		db = db.Where("my_logistics_channel = ?", o.MyLogisticsChannel).Where("order_state >= 2").Where("order_state < 7")
	}

	if logisticsChannel != "" {
		db = db.Where("my_logistics_channel = ?", logisticsChannel)
	}
	/*	if o.MyLogisticsChannel != "" {
		db = db.Where("my_logistics_channel = ?", o.MyLogisticsChannel)
	}*/

	if o.Responsible != "" {
		db = db.Where("responsible LIKE ?", "%"+o.Responsible+"%")
	}

	if o.OrderCode != "" {
		db = db.Where("order_code LIKE ?", "%"+o.OrderCode+"%")
	}

	if o.RetryOrderCode != "" {
		db = db.Where("retry_order_code LIKE ?", "%"+o.RetryOrderCode+"%")
	}

	if o.AfterSaleCode != "" {
		db = db.Where("after_sale_code LIKE ?", "%"+o.AfterSaleCode+"%")
	}

	if o.ThirdOrderCode != "" {
		db = db.Where("third_order_code LIKE ?", "%"+o.ThirdOrderCode+"%")
	}

	if o.OrderState != 0 {
		db = db.Where("order_state = ?", o.OrderState)
	} else if len(orderStateList) > 0 {
		db = db.Where("order_state in (?)", orderStateList)
	}

	if o.OrderType != 0 {
		db = db.Where("order_type = ?", o.OrderType)
	}

	if ctimeS != 0 {
		db = db.Where("created_at >= FROM_UNIXTIME(?)", ctimeS)
	}
	if ctimeE != 0 {
		db = db.Where("created_at <= FROM_UNIXTIME(?)", ctimeE)
	}

	if payS != 0 {
		db = db.Where("pay_time >= ?", payS)
	}
	if payE != 0 {
		db = db.Where("pay_time <= ?", payE)
	}

	if o.WorkOrderState != 0 {
		db = db.Where("work_order_state = ?", o.WorkOrderState)
	}

	if len(userIdArr) != 0 {
		db = db.Where("user_id IN (?)", userIdArr)
	}

	if len(o.CountryCode) > 0 {
		db = db.Where("country_code = ?", o.CountryCode)
	}

	if len(o.RefNo) > 0 {
		db = db.Where("ref_no LIKE ?", "%"+o.RefNo+"%")
	}

	if len(o.TransactionOrder) > 0 {
		db = db.Where("transaction_order LIKE ?", "%"+o.TransactionOrder+"%")
	}
	err = db.Count(&count).Error
	if err != nil {
		return
	}

	err = db.Order("id desc").Offset((pn - 1) * ps).Limit(ps).Find(&list).Error

	return
}

// 后台管理获取超时订单列表的方法
func (o *OrderInfo) AdminGetTimeoutList(pn, ps int, nowUnix int64, orderStateList []string) (list []*OrderListInfo, count int, err error) {

	db := mysql.NewConn().Table(o.TableName()).Where("order_type != 4")

	//
	db = db.Where("pay_time <= ?", nowUnix-60*60*72)
	//
	db = db.Where("order_state >= ? AND order_state <= ?", HAVE_PAY, TESTING_OK)
	//
	db = db.Where("order_pay_state = ? OR order_pay_state = ?", PayStateCompleted, PayStateDefault)

	if o.OrderState != 0 {
		db = db.Where("order_state = ?", o.OrderState)
	} else if len(orderStateList) > 0 {
		db = db.Where("order_state in (?)", orderStateList)
	}

	err = db.Count(&count).Error
	if err != nil {
		return
	}

	err = db.Order("id desc").Offset((pn - 1) * ps).Limit(ps).Find(&list).Error

	return
}

type OrderReplenishItem struct {
	OrderCode  string `json:"order_code"`  // 订单号
	UserID     uint   `json:"user_id"`     // 用户ID
	OrderType  int    `json:"order_type"`  // 订单类型
	ItemCount  int    `json:"item_count"`  // 订单总件数  order_item的数量
	SkuCount   int    `json:"sku_count"`   // 当前sku件数 sku 与 order_item.sku 后六位匹配的数量
	CreatedAt  int64  `json:"created_at"`  // 创建时间
	PayTime    int64  `json:"pay_time"`    // 支付时间
	OrderState int    `json:"order_state"` // 订单状态
}

// 后台获取补货订单列表支持数据
func (o *OrderInfo) AdminGetReplenishList(pn, ps, ctimeS, ctimeE, payS, payE int, sku, sort string, orderStateList []string) (list []*OrderReplenishItem, count int64, err error) {
	// 关联 order_item 表查询
	/*	db := mysql.NewConnV2().Table("order_info as o").
		Joins("LEFT JOIN order_item as oi ON o.order_code = oi.order_code").
		Select("o.order_code, o.user_id, o.order_type, COUNT(oi.id) as item_count, SUM(CASE WHEN RIGHT(oi.sku, 6) = ? THEN 1 ELSE 0 END) as sku_count, o.created_at, o.pay_time, o.order_state", sku).
		Group("o.order_code")*/

	db := mysql.NewConnV2().Table("order_info as o").
		Joins("LEFT JOIN order_item as oi ON o.order_code = oi.order_code").
		Select(`
			o.order_code,
			o.user_id,
			o.order_type,
			COUNT(oi.id) as item_count, 
			SUM(CASE WHEN RIGHT(oi.sku, 6) = ? THEN 1 ELSE 0 END) as sku_count,
			UNIX_TIMESTAMP(o.created_at) as created_at,
			o.pay_time as pay_time,
			o.order_state
		`, sku).
		Group("o.order_code")

	// 添加 order_item 的 SKU 筛选条件
	if sku != "" {
		db = db.Where("EXISTS (SELECT 1 FROM order_item WHERE order_item.order_code = o.order_code AND RIGHT(order_item.sku, 6) = ?)", sku)
	}
	if o.UserID != 0 {
		db = db.Where("o.user_id = ?", o.UserID)
	}

	if o.OrderType != 0 {
		db = db.Where("o.order_type = ?", o.OrderType)
	}

	if ctimeS > 0 {
		db = db.Where("o.created_at >= FROM_UNIXTIME(?)", ctimeS)
	}
	if ctimeE > 0 {
		db = db.Where("o.created_at <= FROM_UNIXTIME(?)", ctimeE)
	}

	if payS > 0 {
		db = db.Where("o.pay_time >= ?", payS)
	}

	if payE > 0 {
		db = db.Where("o.pay_time <= ?", payE)
	}

	if o.OrderState != 0 {
		db = db.Where("order_state = ?", o.OrderState)
	} else if len(orderStateList) > 0 {
		db = db.Where("order_state in (?)", orderStateList)
	}

	err = db.Count(&count).Error
	if err != nil {
		return
	}

	err = db.Order(sort).Offset((pn - 1) * ps).Limit(ps).Find(&list).Error
	return
}

func (o *OrderInfo) QueryByOrderCode() (err error) {
	// SQL对比验证（开发/测试环境）
	if GetSQLValidator().IsEnabled() {
		return o.queryByOrderCodeWithSQLValidation()
	}

	// 生产环境直接执行GORM v2
	return o.queryByOrderCodeV2()
}

// queryByOrderCodeWithSQLValidation 带SQL验证的查询方法
func (o *OrderInfo) queryByOrderCodeWithSQLValidation() error {
	method := "QueryByOrderCode"
	validator := GetSQLValidator()

	// 清理之前的捕获记录
	validator.ClearCapturedSQL(method)

	// 1. 捕获GORM v1的SQL（不执行）
	capturer := NewGormV1SQLCapturer(method)
	conditions := map[string]interface{}{
		"order_code": o.OrderCode,
		"user_id":    o.UserID,
	}
	capturer.CaptureQuerySQL(o, conditions)

	// 2. 执行GORM v2版本
	err := o.queryByOrderCodeV2()

	// 3. 对比SQL差异
	if diff := validator.CompareAndValidate(method); diff != nil {
		log.Error("QueryByOrderCode方法SQL差异", "difference", diff)
	}

	return err
}

// queryByOrderCodeV2 GORM v2版本的查询方法
func (o *OrderInfo) queryByOrderCodeV2() error {
	db := mysql.NewConnV2().Table(o.TableName()).Where("order_code = ?", o.OrderCode)
	if o.UserID > 0 {
		db = db.Where("user_id = ?", o.UserID)
	}

	// 使用SQL捕获日志器
	if GetSQLValidator().IsEnabled() {
		captureLogger := NewSQLCaptureLogger("QueryByOrderCode")
		db = db.Session(&gorm2.Session{Logger: captureLogger})
	}

	return db.First(o).Error
}

// 钱海支付回调订单查询，兼容批量支付
func (o *OrderInfo) QueryListWithOceanBatch() (list []*OrderInfo, err error) {
	db := mysql.NewConn().Table(o.TableName())
	if (len(o.OceanBatchOrder) > 0) && (len(o.OrderCode) > 0) {
		db = db.Where("ocean_batch_order = ? OR order_code = ?", o.OceanBatchOrder, o.OrderCode)
	} else {
		if len(o.OceanBatchOrder) > 0 {
			db = db.Where("ocean_batch_order = ?", o.OceanBatchOrder)
		}
		if len(o.OrderCode) > 0 {
			db = db.Where("order_code = ?", o.OrderCode)
		}
	}
	err = db.Find(&list).Error
	return
}

// 通过订单号 查找订单，并加行锁 FOR UPDATE 排它锁
func (o *OrderInfo) QueryByOrderCodeWithLock(tx *gorm.DB) (err error) {
	db := tx.Table(o.TableName()).Where("order_code = ?", o.OrderCode).Set("gorm:query_option", "FOR UPDATE")
	if o.UserID > 0 {
		db = db.Where("user_id = ?", o.UserID)
	}
	err = db.First(o).Error
	return
}

func (o *OrderInfo) QueryByPaymentID() (err error) {
	db := mysql.NewConn().Table(o.TableName()).Where("ocean_payment_id = ?", o.OceanPaymentId)
	err = db.First(o).Error
	return
}

func (o *OrderInfo) QueryListByDeliveryCar() (list []*OrderInfo, err error) {
	db := mysql.NewConn().Table(o.TableName())

	if len(o.DeliveryCar) > 0 {
		db = db.Where("delivery_car = ?", o.DeliveryCar)
	}

	db = db.Where("order_state <= ?", TESTING_OK)

	err = db.Find(&list).Error
	return
}

func (o *OrderInfo) QueryByPaymentTimeQuantum(payTimeS, payTimeE int64) (list []*OrderInfo, err error) {
	db := mysql.NewConn().Table(o.TableName()).Where("pay_time >= ? AND pay_time < ? ", payTimeS, payTimeE)
	err = db.Preload("OrderItem").Find(&list).Error
	return
}

func (o *OrderInfo) QueryByOrderCodes(orderCodes []string) (list []*OrderInfo, err error) {
	db := mysql.NewConn().Table(o.TableName()).Where("order_code IN (?)", orderCodes)
	if o.UserID > 0 {
		db = db.Where("user_id = ?", o.UserID)
	}
	if o.ShopType > 0 {
		db = db.Where("shop_type = ?", o.ShopType)
	}
	err = db.Find(&list).Error
	return
}

type OrderInfoOnlyOrderCode struct {
	ID        uint   `gorm:"PRIMARY_KEY" json:"id"`
	OrderCode string `json:"order_code"  gorm:"unique_index;not null;"` //平台内订单号
	RefNo     string `json:"ref_no"  gorm:"size:512"`                   //运单号码
}

func (o *OrderInfo) QueryIdsByOrderCodes(orderCodes []string) (list []*OrderInfoOnlyOrderCode, err error) {
	db := mysql.NewConn().Table(o.TableName())

	// 只查询主键id 和订单号
	db = db.Select("id, order_code, ref_no")

	db = db.Where("order_code IN (?)", orderCodes)

	if o.UserID > 0 {
		db = db.Where("user_id = ?", o.UserID)
	}
	if o.ShopType > 0 {
		db = db.Where("shop_type = ?", o.ShopType)
	}
	err = db.Find(&list).Error
	return
}

func (o *OrderInfo) QueryByOrderCodesWithPreload(orderCodes []string, param ...interface{}) (list []*OrderInfo, err error) {
	db := mysql.NewConn().Table(o.TableName()).Where("order_code IN (?)", orderCodes)
	if o.UserID > 0 {
		db = db.Where("user_id = ?", o.UserID)
	}
	if o.ShopType > 0 {
		db = db.Where("shop_type = ?", o.ShopType)
	}

	for _, one := range param {
		db = db.Preload(one.(string))
	}

	err = db.Find(&list).Error
	return
}

func (o *OrderInfo) QueryListToVerifyDeliveryCar() (list []*OrderInfo, err error) {
	db := mysql.NewConn().Table(o.TableName())

	db = db.Where("LENGTH(delivery_car) > 0").Where("order_state < ?", HAVE_WAYBILL)

	err = db.Find(&list).Error

	return
}

func (o *OrderInfo) QueryListWithDeliveryCar() (list []*OrderInfo, err error) {
	db := mysql.NewConn().Table(o.TableName())

	if len(o.DeliveryCar) > 0 {
		db = db.Where("delivery_car = ?", o.DeliveryCar)
	}

	if o.OrderState > 0 {
		db = db.Where("order_state < ?", o.OrderState)
	}

	err = db.Find(&list).Error

	return
}

func (o *OrderInfo) QueryByCarrier() (list []*OrderInfo, count int64, err error) {
	db := mysql.NewConn().Table(o.TableName())

	if len(o.Carrier) < 1 {
		return
	}

	db = db.Where("carrier = ?", o.Carrier)

	err = db.Find(&list).Count(&count).Error
	return
}

func (o *OrderInfo) QueryByUserIDOneMonth(monthCount int) (list []*OrderInfo, err error) {
	db := mysql.NewConn().Table(o.TableName()).Where("order_type != 4")
	if o.UserID > 0 {
		db = db.Where("user_id = ?", o.UserID)
	}

	timeVar := time.Now()

	switch monthCount {
	case 1: // 当前月的订单
	case 2: // N-1 月的订单
		timeVar = timeVar.AddDate(0, -1, 0)
	case 3: // N-2 月的订单
		timeVar = timeVar.AddDate(0, -2, 0)
	default:
		// 当前月的订单
	}
	// 获取月份第一天
	firsDayOfMonth := time.Date(timeVar.Year(), timeVar.Month(), 1, 0, 0, 0, 0, timeVar.Location())

	// 获取月份最后一天
	//lastDayOfMonth := time.Unix(time.Date(timeVar.Year(), timeVar.Month()+1, 1, 0, 0, 0, 0, timeVar.Location()).Unix()-1, 0)
	firsDayOfNextMonth := time.Date(timeVar.Year(), timeVar.Month()+1, 1, 0, 0, 0, 0, timeVar.Location())

	db = db.Where("pay_time >= ?", firsDayOfMonth.Unix()).Where("pay_time < ?", firsDayOfNextMonth.Unix())

	err = db.Find(&list).Error
	return
}

func (o *OrderInfo) QueryByDeliveryTimeCope(shipmentTimeS, shipmentTimeE int64) (list []*OrderInfo, err error) {
	db := mysql.NewConn().Table(o.TableName()).Where("order_type != 4")
	//if o.OrderState > 0 {
	//	db = db.Where("order_state = ?", o.OrderState)
	//}
	err = db.Where("(order_state = ? AND shipments_time >= ? AND shipments_time <= ?) OR (order_code = ?)", o.OrderState, shipmentTimeS, shipmentTimeE,
		"T202212289785040589").
		Preload("OrderItem").
		Preload("OrderItem.TagDesignInfo").  //Preload("OrderItem.TagDesignInfo.HangTagInfo").
		Preload("OrderItem.PackDesignInfo"). //Preload("OrderItem.PackDesignInfo.PackagingInfo").
		Order("user_id asc").
		Find(&list).Error
	return
}

func (o *OrderInfo) GetCountByUserIdPayedOrder() (count int64, err error) {
	db := mysql.NewConn().Table(o.TableName())

	if o.UserID > 0 {
		db = db.Where("user_id = ?", o.UserID)
	}

	db = db.Where("pay_time > 0").Where("order_type = ?", POD_ORDER)

	err = db.Count(&count).Error
	return
}

type WaybillOrder struct {
	OrderCode     string `json:"order_code"  gorm:"unique_index;not null;"` //平台内订单号
	ShipmentsTime int64  `json:"shipments_time"`                            //发货时间
	OrderState    int    `json:"order_state"`                               //订单状态
}

// 获取已贴运单的订单列表
func (o *OrderInfo) GetWaybillOrder() (list []*WaybillOrder, err error) {
	maxTime := time.Now().Add(-(time.Hour * 696)).Unix()
	err = mysql.NewConn().Table(o.TableName()).Where("order_type != 4").Where("order_state = ?", HAVE_WAYBILL).
		Where("shipments_time <= ?", maxTime).
		Find(&list).Error
	return
}

// 获取已贴运单的订单列表，用于更新 paypal 订单状态
func (o *OrderInfo) GetWaybillOrderDetail() (list []*OrderInfo, err error) {
	maxTime := time.Now().Add(-(time.Hour * 1056)).Unix() // 修改为 30天  2023年2月7日20:15:04 更改为45天
	err = mysql.NewConn().Table(o.TableName()).Where("order_type != 4").Where("order_state = ?", HAVE_WAYBILL).
		Where("shipments_time <= ?", maxTime).
		Find(&list).Error
	return
}

// 获取已投递的订单列表，用于更新订单状态
func (o *OrderInfo) GetDeliveredOrderDetail() (list []*OrderInfo, err error) {
	maxTime := time.Now().Add(-(29 * 24 * time.Hour)).Unix() // 签收超过30天的，自动变更已完成
	err = mysql.NewConn().Table(o.TableName()).Where("order_type != 4").Where("order_state = ?", DELIVERED).
		Where("delivered_time <= ?", maxTime).
		Find(&list).Error
	return
}

func (o *OrderInfo) EtsyOrderIsExist() (bool, error) {
	var count int = 0
	db := mysql.NewConn().Table(o.TableName()).Where("shop_type = 2").Where("third_order_code = ?", o.ThirdOrderCode).Count(&count)
	if db.Error == gorm.ErrRecordNotFound {
		return false, nil
	}
	if db.Error != nil {
		return false, db.Error
	}
	return count == 1, nil
}

func (o *OrderInfo) ThirdOrderIsExist() (bool, error) {
	var count int = 0
	//db := mysql.NewConn().Table(o.TableName()).Where("shop_type = ?", o.ShopType).Where("third_order_code = ?", o.ThirdOrderCode).Count(&count)
	db := mysql.NewConn().Table(o.TableName()).Where("shop_type = ?", o.ShopType).Where("third_order_code = ?", o.ThirdOrderCode)
	if o.ShopID > 0 {
		db = db.Where("shop_id = ?", o.ShopID)
	}
	if o.UserID > 0 {
		db = db.Where("user_id = ?", o.UserID)
	}
	db = db.Count(&count).First(o)
	if db.Error == gorm.ErrRecordNotFound {
		return false, nil
	}
	if db.Error != nil {
		return false, db.Error
	}
	return count >= 1, nil
}

func (o *OrderInfo) ThirdOrderNameIsExist() (bool, error) {
	var count int = 0
	//db := mysql.NewConn().Table(o.TableName()).Where("shop_type = ?", o.ShopType).Where("third_order_code = ?", o.ThirdOrderCode).Count(&count)
	db := mysql.NewConn().Table(o.TableName()).Where("shop_type = ?", o.ShopType).Where("third_order_name = ?", o.ThirdOrderName)
	if o.ShopID > 0 {
		db = db.Where("shop_id = ?", o.ShopID)
	}
	db = db.Count(&count)
	if db.Error == gorm.ErrRecordNotFound {
		return false, nil
	}
	if db.Error != nil {
		return false, db.Error
	}
	return count >= 1, nil
}

// 根据一天内的修改时间，查找发生状态变化的订单
func (o *OrderInfo) GetOrderListByUpdatedAt(timeS, timeE int64) (list []*OrderListInfo, err error) {

	db := mysql.NewConn().Table(o.TableName()).Where("order_type != 4")
	db = db.Where("updated_at >= FROM_UNIXTIME(?)", timeS)
	err = db.Where("updated_at <= FROM_UNIXTIME(?)", timeE).Find(&list).Error

	return
}

func (o *OrderInfo) PutOrderRemark() (err error) {
	return mysql.NewConn().Model(&OrderInfo{}).Where("order_code = ?", o.OrderCode).Update("remark", o.Remark).Error
}

func (o *OrderInfo) PutLatestStatus() (err error) {
	return mysql.NewConn().Model(&OrderInfo{}).Where("order_code = ?", o.OrderCode).Update("latest_status", o.LatestStatus).Error
}

func (o *OrderInfo) GetOrderCodeByPay() ([]string, error) {
	var orderCodeList []string

	db := mysql.NewConnV2().Table(o.TableName()).Select("order_code").Where("pay_time > 0").Where("order_state != 12")

	err := db.Find(&orderCodeList).Error

	return orderCodeList, err
}

func (o *OrderInfo) GetCarrierByYun() (list []*OrderInfo, err error) {
	db := mysql.NewConn().Table(o.TableName()).Where("pay_time > 0").Where("carrier = ?", "YUN").Where("order_state <= 7").Where("order_state != 12")
	err = db.Where("pay_time > 1675221446").Find(&list).Error
	return
}

func (o *OrderInfo) GetCNEOrder() (list []*OrderInfo, err error) {
	db := mysql.NewConn().Table(o.TableName()).Where("pay_time > 0").
		Where("carrier = ?", "CNE").
		Where("order_state = 2").
		Where("work_order_state = 1").
		Where("problem_remark LIKE ?", "%风控提醒：账单逾期或可用余额不足%")
	err = db.Where("pay_time > 1684944000").Find(&list).Error
	return
}

type HandOrder struct {
	CreatedAt time.Time `json:"created_at"`

	OrderType       int    `json:"order_type"` //订单类型
	ShopID          uint   `json:"shop_id"`    //商店id 若订单类型为SHOP_ORDER，则记录shopID和ThirdOrderCode
	ShopType        int    `json:"shop_type"`  // 1 shopify  2 etsy
	UserID          uint   `json:"user_id"`
	OrderCode       string `json:"order_code"`       //平台内订单号
	CreatedAtInt    int64  `json:"created_at"`       //创建时间
	PayTime         int64  `json:"pay_time"`         //支付时间
	TotalMoney      int32  `json:"total_money"`      //订单总金额
	CommodityPrices int32  `json:"commodity_prices"` //商品总价格
	OrderState      int    `json:"order_state"`      //订单状态
	LatestStatus    string `json:"latest_status"`    // 物流最新状态
	HandState       int    `json:"hand_state"`       // 手动大货发货状态  1 未发货 2 部分发货 3 发货完成

	ItemCount       int    `json:"item_count"`       // 总件数
	TestingCount    int    `json:"testing_count"`    // 已质检合格件数
	WaybillCount    int    `json:"waybill_count"`    // 已贴运单件数
	LogisticsRemark string `json:"logistics_remark"` // 手动大货运单备注
	LongTime        int64  `json:"long_time"`        // 未录入最长时间戳
	WaybillState    int    `json:"waybill_state"`    // 运单状态  // 1 未录入  2 已录入
}

// QueryHandList() 获取手动大货的订单列表
func (o *OrderInfo) QueryHandList(pn, ps int) (list []*HandOrder, count int, err error) {

	db := mysql.NewConn().Table(o.TableName()).Where("oda_result_sign = ?", "hand").Where("order_state != 12")

	if o.HandState == 0 && o.UserID == 0 && o.OrderCode == "" {
		db = db.Where("hand_state != ?", 3)
	} else {
		if o.UserID != 0 {
			db = db.Where("user_id = ?", o.UserID)
		}
		if o.OrderCode != "" {
			db = db.Where("order_code LIKE ?", "%"+o.OrderCode+"%")
		}
		if o.HandState != 0 {
			db = db.Where("hand_state = ?", o.HandState)
		}
	}

	err = db.Count(&count).Error
	if err != nil {
		return
	}

	err = db.Order("id desc").Offset((pn - 1) * ps).Limit(ps).Find(&list).Error

	return
}

// 根据update内容 更新订单信息
func (o *OrderInfo) UpdateMapForLogistics(update map[string]interface{}, tx ...*gorm.DB) (err error) {
	db := mysql.NewConn()
	if len(tx) != 0 {
		db = tx[0]
	}
	return db.Model(&OrderInfo{}).Where("order_code = ?", o.OrderCode).Updates(update).First(o).Error
}

func (o *OrderInfo) GetOrderByRefNo(refList []string) (list []*OrderInfo, err error) {
	err = mysql.NewConn().Table(o.TableName()).Where("ref_no IN (?)", refList).Preload("OrderItem").Find(&list).Error
	return
}

type OrderCodeAndState struct {
	OrderCode  string `json:"order_code"`  //平台内订单号
	OrderState int    `json:"order_state"` //订单状态
}

func (o *OrderInfo) GetOrderStateByOrderCodes(orderCodes []string, orderState []string) (list []*OrderCodeAndState, err error) {

	db := mysql.NewConn().Table(o.TableName())
	db = db.Where("order_code IN (?)", orderCodes)

	if o.OrderState > 0 {
		db = db.Where("order_state = ?", o.OrderState)
	} else if len(orderState) > 0 {
		db = db.Where("order_state in (?)", orderState)
	}

	err = db.Find(&list).Error

	return
}

type CheckoutHead struct {
	ID            uint   `json:"id"`
	OrderCode     string `json:"order_code"  gorm:"unique_index;not null;"` //平台内订单号
	OdaResultSign string `json:"oda_result_sign"`                           // ODA标识( 手动大货:hand, 拆包物流：unpack)
	DeliveryCar   string `json:"delivery_car"`                              // 发货时分配的发货 车-排-盒
	OrderState    int    `json:"order_state"`                               //订单状态
}

// 查询信息，用来判断是否为 手动大货，
func (o *OrderInfo) GetListToCheckHand(orderCodes []string) (list []*CheckoutHead, err error) {

	db := mysql.NewConn().Table(o.TableName())

	db = db.Where("order_code IN (?)", orderCodes) //.Where("oda_result_sign != 'hand'")

	err = db.Find(&list).Error

	return
}

type userOrderGroupItem struct {
	Count  int64 `json:"count"`
	UserId int64 `json:"user_id"`
}

// 分组统计用户的订单总数
func (o *OrderInfo) GetUserOrderCountGroupByUserId(userIds []uint) (list []*userOrderGroupItem, err error) {
	if len(userIds) == 0 {
		return nil, nil
	}

	err = mysql.NewConn().Table(o.TableName()).
		Select("count(*) as count,user_id").
		Where("order_type != 4").
		Where("user_id in (?)", userIds).
		Group("user_id").
		Find(&list).Error
	return list, err
}

// UpDataRemoteSign 单独强制更新偏远地区标识
func (o *OrderInfo) UpDataRemoteSign(remoteSign bool) (err error) {
	return mysql.NewConn().Model(&OrderInfo{}).Where("order_code = ?", o.OrderCode).Update("remote_sign", remoteSign).Error
}

// 获取已支付订单
type PaidOrder struct {
	OrderCode          string    `json:"order_code"`
	UserId             uint      `json:"user_id"`
	OrderType          int       `json:"order_type"`
	CommodityPrices    int64     `json:"commodity_prices"`
	TotalMoney         int64     `json:"total_money"`
	CreatedAt          time.Time `json:"created_at"`
	PayTime            int64     `json:"pay_time"`
	DeliveredTime      int64     `json:"delivered_time"`
	Country            string    `json:"country"`
	WorkOrderState     int       `json:"work_order_state"`
	OrderState         int       `json:"order_state"`
	RefNo              string    `json:"ref_no"`
	ProblemRemark      string    `json:"problem_remark"` //问题订单备注 (有就显示)
	MyLogisticsChannel string    `json:"my_logistics_channel"`
	ThirdOrderCode     string    `json:"third_order_code"`              //第三方平台订单号 若平台内订单则为空
	CountryCode        string    `json:"country_code"`                  //国家（国际二字码 标准ISO 3166-2 ）
	OldRefNo           string    `json:"old_ref_no"  gorm:"type:BLOB;"` // 物流追踪替换单号的旧单号
	TransactionOrder   string    `json:"transaction_order"`             // 第三平台（paypal等）的交易流水号 transaction payment，capture payment transaction
	PwpDiscountMoney   int       `json:"pwp_discount_money"`            // p卡折扣金额  单位美分 p卡余额实际支付金额为total_money - pwp_discount_money
}

func (o *OrderInfo) GetPaidOrderList(LogisticsChannel string, orderStateList []string, CTimeS int, CTimeE int, PayTimeS int, PayTimeE int, DeliveredTimeS int, DeliveredTimeE int) (list []*PaidOrder, err error) {

	db := mysql.NewConn().Table(o.TableName())
	//db = db.Select("order_code,user_id,order_type,commodity_prices,total_money,created_at,MIN(pay_time),work_order_state,order_state,delivered_time,problem_remark,ref_no,my_logistics_channel,third_order_code,country_code,country,old_ref_no,transaction_order")

	//订单状态多选
	orderStateSql := ""
	if len(orderStateList) > 0 {
		//db = db.Where("order_state IN (?)", orderStateList)
		orderStateSql = fmt.Sprintf("order_state IN (%v)", orderStateList)
	} else if o.OrderState != 0 { //订单状态单选
		//db = db.Where("order_state = ?", o.OrderState)
		orderStateSql = fmt.Sprintf("order_state = %d", o.OrderState)
	} else {
		//db = db.Where("order_state BETWEEN 2 AND 10")
		orderStateSql = "order_state BETWEEN 2 AND 10"
	}

	sqlStr := fmt.Sprintf("WITH ranked_orders AS (\n"+
		"    SELECT oi.order_code,\n"+
		"           oi.user_id,\n"+
		"           oi.order_type,\n"+
		"           oi.commodity_prices,\n"+
		"           oi.total_money,\n"+
		"           oi.created_at,\n"+
		"           oi.pay_time,\n"+
		"           oi.work_order_state,\n"+
		"           oi.order_state,\n"+
		" 			oi.order_pay_state,\n"+
		"           oi.delivered_time,\n"+
		"           oi.problem_remark,\n"+
		"           oi.ref_no,\n"+
		"           oi.my_logistics_channel,\n"+
		"           oi.third_order_code,\n"+
		"           oi.country_code,\n"+
		"           oi.country,\n"+
		"           oi.old_ref_no,\n"+
		"           oi.transaction_order,\n"+
		"           oi.pwp_discount_money,\n"+
		"           ROW_NUMBER() OVER (PARTITION BY oi.user_id ORDER BY oi.pay_time ASC) AS rn\n"+
		"    FROM order_info oi\n"+
		"    WHERE (oi.order_state BETWEEN 2 AND 10) AND (%s)\n"+
		")\n"+
		"SELECT *\n"+
		"FROM ranked_orders\n"+
		"WHERE rn = 1\n", orderStateSql)
	//"ORDER BY UNIX_TIMESTAMP(created_at) ASC\nLIMIT 20 OFFSET 0;", orderStateSql)

	if o.UserID != 0 {
		//db = db.Where("user_id=?", o.UserID)
		sqlStr = fmt.Sprintf("%s AND user_id = %d", sqlStr, o.UserID)
	}
	if o.MyLogisticsChannel != "" {
		//db = db.Where("my_logistics_channel=?", o.MyLogisticsChannel)
		sqlStr = fmt.Sprintf("%s AND my_logistics_channel = '%s'", sqlStr, o.MyLogisticsChannel)
	}
	if o.OrderCode != "" {
		//db = db.Where("order_code LIKE ?", "%"+o.OrderCode+"%")
		sqlStr = fmt.Sprintf("%s AND order_code LIKE '%s'", sqlStr, "%"+o.OrderCode+"%")
	}
	if o.ThirdOrderCode != "" {
		//db = db.Where("third_order_code LIKE ?", "%"+o.ThirdOrderCode+"%")
		sqlStr = fmt.Sprintf("%s AND third_order_code LIKE '%s'", sqlStr, "%"+o.ThirdOrderCode+"%")
	}
	if o.OrderType != 0 {
		//db = db.Where("order_type=?", o.OrderType)
		sqlStr = fmt.Sprintf("%s AND order_type = %d", sqlStr, o.OrderType)
	}

	if o.WorkOrderState != 0 {
		//db = db.Where("work_order_state=?", o.WorkOrderState)
		sqlStr = fmt.Sprintf("%s AND work_order_state = %d", sqlStr, o.WorkOrderState)
	}

	if len(o.CountryCode) > 0 {
		//db = db.Where("country_code=?", o.CountryCode)
		sqlStr = fmt.Sprintf("%s AND country_code = '%s'", sqlStr, o.CountryCode)
	}

	if len(o.RefNo) > 0 {
		//db = db.Where("ref_no LIKE ? OR old_ref_no LIKE ?", "%"+o.RefNo+"%", "%"+o.RefNo+"%")
		sqlStr = fmt.Sprintf("%s AND (ref_no LIKE '%s' OR old_ref_no LIKE '%s')", sqlStr, "%"+o.RefNo+"%", "%"+o.RefNo+"%")
	}

	if len(o.TransactionOrder) > 0 {
		//db = db.Where("transaction_order LIKE ?", "%"+o.TransactionOrder+"%")
		sqlStr = fmt.Sprintf("%s AND transaction_order LIKE '%s'", sqlStr, o.TransactionOrder)
	}
	if LogisticsChannel != "" {
		//db = db.Where("my_logistics_channel = ?", LogisticsChannel)
		sqlStr = fmt.Sprintf("%s AND my_logistics_channel = '%s'", sqlStr, LogisticsChannel)
	}

	if CTimeS != 0 {
		//db = db.Where("created_at >= FROM_UNIXTIME(?)", CTimeS)
		sqlStr = fmt.Sprintf("%s AND UNIX_TIMESTAMP(created_at) >= %d", sqlStr, CTimeS)
	}
	if CTimeE != 0 {
		//db = db.Where("created_at <= FROM_UNIXTIME(?)", CTimeE)
		sqlStr = fmt.Sprintf("%s AND UNIX_TIMESTAMP(created_at) <=  %d", sqlStr, CTimeE)
	}
	if PayTimeS != 0 {
		//db = db.Where("pay_time >= ?", PayTimeS)
		sqlStr = fmt.Sprintf("%s AND pay_time >=  %d", sqlStr, PayTimeS)
	}
	if PayTimeE != 0 {
		//db = db.Where("pay_time <= ?", PayTimeE)
		sqlStr = fmt.Sprintf("%s AND pay_time <= %d", sqlStr, PayTimeE)
	}

	if DeliveredTimeS != 0 {
		//db = db.Where("delivered_time >= ?", DeliveredTimeS)
		sqlStr = fmt.Sprintf("%s AND delivered_time >= %d", sqlStr, DeliveredTimeS)
	}
	if DeliveredTimeE != 0 {
		//db = db.Where("delivered_time <= ?", DeliveredTimeE)
		sqlStr = fmt.Sprintf("%s AND delivered_time <= %d", sqlStr, DeliveredTimeE)
	}

	//db = db.Group("user_id")
	sqlStr = fmt.Sprintf("%s GROUP BY user_id", sqlStr)
	//db = db.Order("UNIX_TIMESTAMP(created_at) DESC")
	sqlStr = fmt.Sprintf("%s ORDER BY UNIX_TIMESTAMP(created_at) DESC", sqlStr)

	db = db.Raw(sqlStr)

	err = db.Find(&list).Error
	return
}

type SummaryOrderData struct {
	UserId           uint   `json:"user_id"`
	OrderCount       int64  `json:"order_count"`        //支付成功的订单数量
	ItemCount        int64  `json:"item_count"`         //订单子项数量
	TotalAmount      string `json:"total_amount"`       //订单金额
	MinPayTime       int64  `json:"min_pay_time"`       //最小支付时间
	PwpDiscountMoney int    `json:"pwp_discount_money"` // p卡折扣金额  单位美分 p卡余额实际支付金额为total_money - pwp_discount_money
}

// 获取用户汇总支付成功的订单
func (o *OrderInfo) GetUserOrderSummaryByConditions(sourceList []string, designIds []string, versions []string) (list []*SummaryOrderData, err error) {
	db := mysql.NewConn()

	// 手动拼接 OR 条件
	var orConditions string
	for i := range designIds {
		if i > 0 {
			orConditions += " OR "
		}
		orConditions += "(B.design_id = '" + designIds[i] + "' AND B.versions = '" + versions[i] + "' AND B.source = '" + sourceList[i] + "')"
	}

	db = db.Table("order_info as A").
		Select("A.user_id,A.pwp_discount_money, COUNT(DISTINCT A.order_code) as order_count, COUNT(DISTINCT B.id) as item_count, SUM(DISTINCT IFNULL(A.total_money, 0)) as total_amount, MIN(A.pay_time) as min_pay_time").
		Joins("JOIN order_item as B ON A.order_code = B.order_code").
		Where("A.pay_time > 0 and A.order_state != 12").Where(orConditions)

	//Where("A.pay_time > 0 AND B.source IN (?) AND B.design_id in (?) AND B.versions in (?)", sourceList, designIds, versions).
	err = db.Group("A.user_id").
		Scan(&list).Error

	return list, err

}

// UpdateAuditState
func (o *OrderInfo) UpdateAuditState(orderList []string) (err error) {
	return mysql.NewConn().Model(&OrderInfo{}).Where("order_code IN (?)", orderList).Update("audit_state", o.AuditState).Error
}

// 筛选一个客户某个时间段内的订单
func (o *OrderInfo) GetPaiedListBetweenTime(timeS, timeE int64) (list []*OrderInfo, err error) {

	db := mysql.NewConn().Table(o.TableName())

	db = db.Where("pay_time > 0 AND order_state <= ?", COMPLETED)

	if o.UserID > 0 {
		db = db.Where("user_id = ?", o.UserID)
	}

	if (timeS > 0) && (timeE > 0) {
		db = db.Where("pay_time >= ? AND pay_time < ?", timeS, timeE)
	} else if timeS > 0 {
		db = db.Where("pay_time >= ?", timeS)
	} else if timeE > 0 {
		db = db.Where("pay_time < ?", timeE)
	}

	err = db.Find(&list).Error
	return
}

func (o *OrderInfo) QueryListByInfo() (list []*OrderInfo, err error) {

	db := mysql.NewConn().Model(o)
	if o.Payment > 0 {
		db = db.Where("payment = ?", o.Payment)
	}
	if len(o.MD5Key) > 0 {
		db = db.Where("md5_Key = ?", o.MD5Key)
	}
	if o.OrderPayState > 0 {
		db = db.Where("order_pay_state = ?", o.OrderPayState)
	}
	if o.OrderState != 0 {
		db = db.Where("order_state = ?", o.OrderState)
	}
	if len(o.OrderCode) > 0 {
		db = db.Where("order_code = ?", o.OrderCode)
	}
	if o.UserID > 0 {
		db = db.Where("user_id = ?", o.UserID)
	}
	if len(o.MoneyOrder) > 0 {
		db = db.Where("money_order = ?", o.MoneyOrder)
	}
	if o.OrderType > 0 {
		db = db.Where("order_type = ?", o.OrderType)
	}

	err = db.Order("id desc").Find(&list).Error
	return
}

type OrderAndItemBySourceItem struct {
	UserId           uint        `json:"user_id"`
	OrderCode        string      `json:"order_code"`
	TotalMoney       int32       `json:"total_money"`                                                                 // 订单总金额
	PwpDiscountMoney int32       `json:"pwp_discount_money"`                                                          // p卡折扣金额  单位美分 p卡余额实际支付金额为total_money - pwp_discount_money
	PayTime          int64       `json:"pay_time"`                                                                    // 支付时间
	OrderItems       []OrderItem `json:"order_items"  gorm:"foreignkey:order_code;association_foreignkey:order_code"` // 预加载的订单子项
}

// 获取包含指定 source、designId 和 versions 的订单，并预加载订单子项
func (o *OrderInfo) GetPaidOrdersWithItemsByConditions(userId uint, sourceList []string, designIdList []int, versionsList []int) (list []*OrderAndItemBySourceItem, err error) {
	db := mysql.NewConn()
	err = db.Table("order_info as A").
		Select("A.*, B.*").
		Joins("JOIN order_item as B ON A.order_code = B.order_code").
		Where("A.pay_time > 0 AND A.user_id = ? AND A.order_state != 12 AND B.source IN (?) AND B.design_id IN (?) AND B.versions IN (?)", userId, sourceList, designIdList, versionsList).
		Preload("OrderItems").
		Find(&list).Error
	return list, err
}

func (o *OrderInfo) QueryFirstByInfo() (err error) {

	db := mysql.NewConn().Model(o)
	if o.Payment > 0 {
		db = db.Where("payment = ?", o.Payment)
	}
	if len(o.MD5Key) > 0 {
		db = db.Where("md5_Key = ?", o.MD5Key)
	}
	if o.OrderPayState > 0 {
		db = db.Where("order_pay_state = ?", o.OrderPayState)
	}
	if o.OrderState != 0 {
		db = db.Where("order_state = ?", o.OrderState)
	}
	if len(o.OrderCode) > 0 {
		db = db.Where("order_code = ?", o.OrderCode)
	}
	if o.UserID > 0 {
		db = db.Where("user_id = ?", o.UserID)
	}
	if len(o.MoneyOrder) > 0 {
		db = db.Where("money_order = ?", o.MoneyOrder)
	}
	if o.OrderType > 0 {
		db = db.Where("order_type = ?", o.OrderType)
	}

	err = db.Order("id asc").First(o).Error
	return
}

// 根据用户ID查询 total_money 合计
func (o *OrderInfo) QueryTotalMoneyByUserID() (totalMoney int64, err error) {
	db := mysql.NewConn().Model(o)
	if o.UserID > 0 {
		db = db.Where("user_id = ?", o.UserID)
	}
	db = db.Where("pay_time > 0").Where("state != 12").Where("order_pay_state = 0 or order_pay_state = 10")
	err = db.Select("SUM(total_money)").Row().Scan(&totalMoney)
	return
}

type userIDItem struct {
	UserID uint `json:"user_id"`
}

// 查找历史用户支付金额 >1000 的用户列表
func (o *OrderInfo) QueryUserListByTotalMoney() (list []*userIDItem, err error) {
	db := mysql.NewConn().Model(o).Select("user_id")
	db = db.Where("pay_time > 0").Where("state != 12").Where("order_pay_state = 0 or order_pay_state = 10")
	db = db.Group("user_id").Having("SUM(total_money) > 100000")
	err = db.Scan(&list).Error
	return
}

// QueryUserListByPaypal
func (o *OrderInfo) QueryUserListByPaypal() (list []*userIDItem, err error) {
	err = mysql.NewConn().Model(o).Select("DISTINCT user_id").
		Where("pay_time > ?", 1719849600).Where("state != 12").Where("order_pay_state = 0 or order_pay_state = 10").
		Scan(&list).Error
	return
}

type UserPaidOrder struct {
	UserID            uint    `json:"user_id"`
	TotalMoneyPeriod1 float64 `json:"total_money_period1"`
	TotalMoneyPeriod2 float64 `json:"total_money_period2"`
	Ratio             string  `json:"ratio"`
}

func (o *OrderInfo) GetUserListByReducedPaidOrders() (list []*UserPaidOrder, err error) {
	// 第一个时间段和第二个时间段
	startPeriod1 := "2024-04-27 00:00:00"
	endPeriod1 := "2024-07-01 23:59:59"
	startPeriod2 := "2024-07-02 00:00:00"
	endPeriod2 := "2024-09-04 23:59:59"

	// 子查询，计算两个时间段的总金额
	userTotalsSubQuery := mysql.NewConn().Table("pod_partner.order_info").
		Select(`user_id,
			SUM(CASE
				WHEN pay_time BETWEEN UNIX_TIMESTAMP(?) AND UNIX_TIMESTAMP(?) THEN total_money ELSE 0 END) AS total_money_period1,
			SUM(CASE
				WHEN pay_time BETWEEN UNIX_TIMESTAMP(?) AND UNIX_TIMESTAMP(?) THEN total_money ELSE 0 END) AS total_money_period2`,
			startPeriod1, endPeriod1, startPeriod2, endPeriod2).
		Where("order_state NOT IN (?) AND pay_time BETWEEN UNIX_TIMESTAMP(?) AND UNIX_TIMESTAMP(?)",
			[]int{1, 11, 12, 13, 14}, startPeriod1, endPeriod2).
		Group("user_id").
		SubQuery()

	// 使用 Raw 方法执行主查询
	sqlStr := `
		SELECT 
			user_id AS user_id,
			total_money_period1 / 100 AS 'total_money_period1',
			total_money_period2 / 100 AS 'total_money_period2',
			CASE
				WHEN total_money_period1 = 0 THEN NULL
				ELSE CONCAT(ROUND((total_money_period2 / total_money_period1) * 100, 2), '%')
			END AS 比例
		FROM 
			(?) AS user_totals
		WHERE 
			total_money_period2 < total_money_period1 / 2
		ORDER BY 
			total_money_period1 DESC`

	// 使用 Raw 方法进行查询，传递子查询参数
	db := mysql.NewConn().Raw(sqlStr, userTotalsSubQuery)
	err = db.Find(&list).Error

	return
}

// 查询用户最近的两笔样品订单
func (o *OrderInfo) QueryUserSampleOrders() (list []*OrderInfo, err error) {
	// 查询用户最近的两笔样品订单
	err = mysql.NewConn().Table(o.TableName()).
		Where("user_id = ? AND order_type = 1 AND order_state != 12 AND pay_time != 0", o.UserID).
		Order("pay_time desc").
		Limit(2).Find(&list).Error
	if err != nil {
		return nil, err
	}
	return
}

func (o *OrderInfo) QueryByRefNo() (list []*OrderInfo, err error) {
	db := mysql.NewConn().Table(o.TableName()).Where("ref_no = ?", o.RefNo)
	err = db.Find(&list).Error
	return
}

func (o *OrderInfo) Query() (err error) {
	db := mysql.NewConn().Table(o.TableName())

	// 用来记录是否有根据参数进行了筛选，如果都没有，就返回错误
	hasWhere := false

	if o.ID > 0 {
		hasWhere = true
		db = db.Where("id = ?", o.ID)
	}

	if len(o.OrderCode) > 0 {
		hasWhere = true
		db = db.Where("order_code = ?", o.OrderCode)
	}

	if !hasWhere {
		err = errors.New("no where condition")
		return
	}

	err = db.Find(o).Error
	return
}

// 查询收货地为墨西哥|巴西的没有税号的 shopify 同步订单，订单状态为地址不可到达
func (o *OrderInfo) QueryShippingCredentail(countryCodes []string, createdTimeS int64) (list []*OrderInfo, err error) {
	db := mysql.NewConn().Table(o.TableName())

	// 根据创建时间筛选
	if createdTimeS > 0 {
		db = db.Where("created_at >= FROM_UNIXTIME(?)", createdTimeS)
	}

	// 筛选国家为墨西哥或巴西
	db = db.Where("country_code IN (?)", countryCodes)

	// 筛选 shopify 订单
	db = db.Where("shop_type = ?", ORDER_SHOP_TYPE_SHOPIFY)

	// 筛选订单状态为地址不可到达
	db = db.Where("order_state = ?", UNDELIVERABLE_ADDRESS)

	err = db.Find(&list).Error
	return
}

// GetPaidOrdersWithoutWorkOrder
// 查询订单状态为1 工单生成状态为1的订单 制版状态为1 的订单，合计订单子项表digitization_count 数量到all_digitization_count
func (o *OrderInfo) GetPaidOrdersWithoutWorkOrder() (list []*OrderListInfo, err error) {
	db := mysql.NewConn().Table(o.TableName()).
		Select(`
			order_info.id,
			order_info.order_code,
			order_info.user_id,
			order_info.total_money,
			SUM(order_item.digitization_count) as all_digitization_count
		`).
		Joins("JOIN order_item ON order_info.order_code = order_item.order_code").
		Where("order_info.order_state = ?", 2).         // Paid
		Where("order_info.work_order_state = ?", 1).    // Work order generation state
		Where("order_info.digitization_status = ?", 1). // Digitization state
		Group("order_info.id")

	err = db.Find(&list).Error
	if err != nil {
		log.Error("Failed to query paid orders without work order:", err)
		return nil, err
	}

	return list, nil

}
