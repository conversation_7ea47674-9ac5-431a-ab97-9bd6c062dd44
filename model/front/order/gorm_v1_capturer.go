package order

import (
	"fmt"
	"reflect"
	"strings"
	"time"
	"zx/unit/pkg/mysql"

	"github.com/jinzhu/gorm"
)

// GormV1SQLCapturer GORM v1 SQL捕获器
type GormV1SQLCapturer struct {
	validator *SQLValidator
	method    string
	db        *gorm.DB
}

// NewGormV1SQLCapturer 创建GORM v1 SQL捕获器
func NewGormV1SQLCapturer(method string) *GormV1SQLCapturer {
	return &GormV1SQLCapturer{
		validator: GetSQLValidator(),
		method:    method,
		db:        mysql.NewConn(),
	}
}

// CaptureCreateSQL 捕获Create操作的SQL
func (c *GormV1SQLCapturer) CaptureCreateSQL(o *OrderInfo, tx *gorm.DB) {
	if !c.validator.IsEnabled() {
		return
	}

	db := c.db
	if tx != nil {
		db = tx
	}

	// 使用DryRun模式捕获SQL而不执行
	dryRunDB := db.Set("gorm:query_option", "DRY_RUN")
	
	// 模拟Create操作
	scope := dryRunDB.NewScope(o)
	scope.TableName()
	
	// 构建INSERT SQL
	sql := c.buildCreateSQL(o, scope)
	args := c.extractCreateArgs(o)
	
	c.validator.CaptureSQL(c.method, "v1", sql, args, nil)
}

// CaptureQuerySQL 捕获查询操作的SQL
func (c *GormV1SQLCapturer) CaptureQuerySQL(o *OrderInfo, conditions map[string]interface{}) {
	if !c.validator.IsEnabled() {
		return
	}

	// 构建查询SQL
	sql := fmt.Sprintf("SELECT * FROM %s", o.TableName())
	args := make([]interface{}, 0)
	
	whereClauses := make([]string, 0)
	
	if orderCode, ok := conditions["order_code"]; ok && orderCode != "" {
		whereClauses = append(whereClauses, "order_code = ?")
		args = append(args, orderCode)
	}
	
	if userID, ok := conditions["user_id"]; ok && userID.(uint) > 0 {
		whereClauses = append(whereClauses, "user_id = ?")
		args = append(args, userID)
	}
	
	// 添加软删除条件（GORM v1默认行为）
	whereClauses = append(whereClauses, "deleted_at IS NULL")
	
	if len(whereClauses) > 0 {
		sql += " WHERE " + strings.Join(whereClauses, " AND ")
	}
	
	c.validator.CaptureSQL(c.method, "v1", sql, args, nil)
}

// CaptureUpdateSQL 捕获更新操作的SQL
func (c *GormV1SQLCapturer) CaptureUpdateSQL(o *OrderInfo, conditions map[string]interface{}, updates map[string]interface{}) {
	if !c.validator.IsEnabled() {
		return
	}

	// 构建UPDATE SQL
	setClauses := make([]string, 0)
	args := make([]interface{}, 0)
	
	// 添加更新字段
	for field, value := range updates {
		setClauses = append(setClauses, fmt.Sprintf("%s = ?", field))
		args = append(args, value)
	}
	
	// 添加updated_at
	setClauses = append(setClauses, "updated_at = ?")
	args = append(args, time.Now())
	
	sql := fmt.Sprintf("UPDATE %s SET %s", o.TableName(), strings.Join(setClauses, ", "))
	
	// 添加WHERE条件
	whereClauses := make([]string, 0)
	
	if id, ok := conditions["id"]; ok && id.(uint) > 0 {
		whereClauses = append(whereClauses, "id = ?")
		args = append(args, id)
	}
	
	if orderCode, ok := conditions["order_code"]; ok && orderCode != "" {
		whereClauses = append(whereClauses, "order_code = ?")
		args = append(args, orderCode)
	}
	
	if userID, ok := conditions["user_id"]; ok && userID.(uint) > 0 {
		whereClauses = append(whereClauses, "user_id = ?")
		args = append(args, userID)
	}
	
	// 添加软删除条件
	whereClauses = append(whereClauses, "deleted_at IS NULL")
	
	if len(whereClauses) > 0 {
		sql += " WHERE " + strings.Join(whereClauses, " AND ")
	}
	
	c.validator.CaptureSQL(c.method, "v1", sql, args, nil)
}

// CaptureDeleteSQL 捕获删除操作的SQL
func (c *GormV1SQLCapturer) CaptureDeleteSQL(o *OrderInfo, ids []uint) {
	if !c.validator.IsEnabled() {
		return
	}

	// GORM v1的软删除SQL
	sql := fmt.Sprintf("UPDATE %s SET deleted_at = ? WHERE id IN (?)", o.TableName())
	args := []interface{}{time.Now(), ids}
	
	c.validator.CaptureSQL(c.method, "v1", sql, args, nil)
}

// buildCreateSQL 构建Create SQL语句
func (c *GormV1SQLCapturer) buildCreateSQL(o *OrderInfo, scope *gorm.Scope) string {
	tableName := scope.TableName()
	
	// 获取所有字段
	fields := scope.Fields()
	columns := make([]string, 0)
	placeholders := make([]string, 0)
	
	for _, field := range fields {
		if !field.IsPrimaryKey || !field.IsBlank {
			columns = append(columns, field.DBName)
			placeholders = append(placeholders, "?")
		}
	}
	
	sql := fmt.Sprintf("INSERT INTO %s (%s) VALUES (%s)",
		tableName,
		strings.Join(columns, ", "),
		strings.Join(placeholders, ", "))
	
	return sql
}

// extractCreateArgs 提取Create操作的参数
func (c *GormV1SQLCapturer) extractCreateArgs(o *OrderInfo) []interface{} {
	args := make([]interface{}, 0)
	
	// 使用反射获取结构体字段值
	v := reflect.ValueOf(o).Elem()
	t := reflect.TypeOf(o).Elem()
	
	for i := 0; i < v.NumField(); i++ {
		field := v.Field(i)
		fieldType := t.Field(i)
		
		// 跳过主键字段（如果为空）
		if fieldType.Name == "ID" && field.Uint() == 0 {
			continue
		}
		
		// 跳过嵌入字段
		if fieldType.Anonymous {
			continue
		}
		
		// 跳过关联字段
		if strings.Contains(fieldType.Tag.Get("gorm"), "foreignkey") {
			continue
		}
		
		args = append(args, field.Interface())
	}
	
	return args
}

// CapturePreloadSQL 捕获预加载SQL
func (c *GormV1SQLCapturer) CapturePreloadSQL(o *OrderInfo, preloads []string) {
	if !c.validator.IsEnabled() {
		return
	}

	// 主查询SQL
	mainSQL := fmt.Sprintf("SELECT * FROM %s WHERE order_code = ? AND deleted_at IS NULL", o.TableName())
	mainArgs := []interface{}{o.OrderCode}
	
	if o.UserID > 0 {
		mainSQL = fmt.Sprintf("SELECT * FROM %s WHERE order_code = ? AND user_id = ? AND deleted_at IS NULL", o.TableName())
		mainArgs = []interface{}{o.OrderCode, o.UserID}
	}
	
	c.validator.CaptureSQL(c.method+"_main", "v1", mainSQL, mainArgs, nil)
	
	// 预加载SQL
	for _, preload := range preloads {
		switch preload {
		case "OrderItem":
			preloadSQL := "SELECT * FROM order_item WHERE order_code = ? AND deleted_at IS NULL"
			preloadArgs := []interface{}{o.OrderCode}
			c.validator.CaptureSQL(c.method+"_preload_"+preload, "v1", preloadSQL, preloadArgs, nil)
			
		case "OrderItem.SkuInfo":
			// 这里需要根据实际的关联关系构建SQL
			preloadSQL := "SELECT * FROM sku_info WHERE id IN (SELECT sku_id FROM order_item WHERE order_code = ? AND deleted_at IS NULL)"
			preloadArgs := []interface{}{o.OrderCode}
			c.validator.CaptureSQL(c.method+"_preload_"+preload, "v1", preloadSQL, preloadArgs, nil)
		}
	}
}

// CaptureBatchUpdateSQL 捕获批量更新SQL
func (c *GormV1SQLCapturer) CaptureBatchUpdateSQL(o *OrderInfo, ids []uint, updates map[string]interface{}) {
	if !c.validator.IsEnabled() {
		return
	}

	setClauses := make([]string, 0)
	args := make([]interface{}, 0)
	
	for field, value := range updates {
		setClauses = append(setClauses, fmt.Sprintf("%s = ?", field))
		args = append(args, value)
	}
	
	setClauses = append(setClauses, "updated_at = ?")
	args = append(args, time.Now())
	
	sql := fmt.Sprintf("UPDATE %s SET %s WHERE id IN (?) AND deleted_at IS NULL",
		o.TableName(),
		strings.Join(setClauses, ", "))
	
	args = append(args, ids)
	
	c.validator.CaptureSQL(c.method, "v1", sql, args, nil)
}

// CaptureBatchQuerySQL 捕获批量查询SQL
func (c *GormV1SQLCapturer) CaptureBatchQuerySQL(o *OrderInfo, orderCodes []string, conditions map[string]interface{}) {
	if !c.validator.IsEnabled() {
		return
	}

	sql := fmt.Sprintf("SELECT * FROM %s WHERE order_code IN (?)", o.TableName())
	args := []interface{}{orderCodes}
	
	whereClauses := []string{"order_code IN (?)"}
	
	if userID, ok := conditions["user_id"]; ok && userID.(uint) > 0 {
		whereClauses = append(whereClauses, "user_id = ?")
		args = append(args, userID)
	}
	
	if shopType, ok := conditions["shop_type"]; ok && shopType.(int) > 0 {
		whereClauses = append(whereClauses, "shop_type = ?")
		args = append(args, shopType)
	}
	
	whereClauses = append(whereClauses, "deleted_at IS NULL")
	
	sql = fmt.Sprintf("SELECT * FROM %s WHERE %s", o.TableName(), strings.Join(whereClauses, " AND "))
	
	c.validator.CaptureSQL(c.method, "v1", sql, args, nil)
}
