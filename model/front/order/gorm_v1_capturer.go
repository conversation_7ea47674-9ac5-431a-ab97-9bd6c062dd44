package order

import (
	"fmt"
	"strings"
	"sync"
	"zx/unit/pkg/mysql"
	"zx/zxgo/log"

	"github.com/jinzhu/gorm"
)

// GormV1SQLCapturer GORM v1 SQL捕获器
type GormV1SQLCapturer struct {
	validator    *SQLValidator
	method       string
	db           *gorm.DB
	capturedSQL  string
	capturedArgs []interface{}
	mu           sync.RWMutex
}

// NewGormV1SQLCapturer 创建GORM v1 SQL捕获器
func NewGormV1SQLCapturer(method string) *GormV1SQLCapturer {
	// 使用defer和recover来处理数据库连接可能的panic
	var db *gorm.DB
	func() {
		defer func() {
			if r := recover(); r != nil {
				log.Debug("数据库连接失败，跳过GORM v1 SQL捕获", "error", r)
				db = nil
			}
		}()
		db = mysql.NewConn()
	}()

	if db == nil {
		log.Debug("数据库连接为空，跳过GORM v1 SQL捕获")
		return &GormV1SQLCapturer{
			validator: GetSQLValidator(),
			method:    method,
			db:        nil,
		}
	}

	capturer := &GormV1SQLCapturer{
		validator: GetSQLValidator(),
		method:    method,
		db:        db,
	}

	// 注册SQL捕获回调
	capturer.registerCallbacks()

	return capturer
}

// registerCallbacks 注册GORM v1回调来捕获SQL
func (c *GormV1SQLCapturer) registerCallbacks() {
	if c.db == nil {
		return
	}

	// 注册创建回调
	c.db.Callback().Create().Before("gorm:create").Register("sql_capture:before_create", c.beforeCreateCallback)

	// 注册查询回调
	c.db.Callback().Query().Before("gorm:query").Register("sql_capture:before_query", c.beforeQueryCallback)

	// 注册更新回调
	c.db.Callback().Update().Before("gorm:update").Register("sql_capture:before_update", c.beforeUpdateCallback)

	// 注册删除回调
	c.db.Callback().Delete().Before("gorm:delete").Register("sql_capture:before_delete", c.beforeDeleteCallback)
}

// beforeCreateCallback 创建前回调，捕获SQL
func (c *GormV1SQLCapturer) beforeCreateCallback(scope *gorm.Scope) {
	if !c.validator.IsEnabled() {
		return
	}

	// 构建SQL语句
	sql := c.buildCreateSQLFromScope(scope)
	args := c.extractArgsFromScope(scope)

	c.mu.Lock()
	c.capturedSQL = sql
	c.capturedArgs = args
	c.mu.Unlock()

	log.Debug("GORM v1 捕获CREATE SQL", "sql", sql, "args", args)
}

// beforeQueryCallback 查询前回调，捕获SQL
func (c *GormV1SQLCapturer) beforeQueryCallback(scope *gorm.Scope) {
	if !c.validator.IsEnabled() {
		return
	}

	sql := scope.SQL
	args := scope.SQLVars

	c.mu.Lock()
	c.capturedSQL = sql
	c.capturedArgs = args
	c.mu.Unlock()

	log.Debug("GORM v1 捕获QUERY SQL", "sql", sql, "args", args)
}

// beforeUpdateCallback 更新前回调，捕获SQL
func (c *GormV1SQLCapturer) beforeUpdateCallback(scope *gorm.Scope) {
	if !c.validator.IsEnabled() {
		return
	}

	sql := scope.SQL
	args := scope.SQLVars

	c.mu.Lock()
	c.capturedSQL = sql
	c.capturedArgs = args
	c.mu.Unlock()

	log.Debug("GORM v1 捕获UPDATE SQL", "sql", sql, "args", args)
}

// beforeDeleteCallback 删除前回调，捕获SQL
func (c *GormV1SQLCapturer) beforeDeleteCallback(scope *gorm.Scope) {
	if !c.validator.IsEnabled() {
		return
	}

	sql := scope.SQL
	args := scope.SQLVars

	c.mu.Lock()
	c.capturedSQL = sql
	c.capturedArgs = args
	c.mu.Unlock()

	log.Debug("GORM v1 捕获DELETE SQL", "sql", sql, "args", args)
}

// CaptureCreateSQL 捕获Create操作的SQL
func (c *GormV1SQLCapturer) CaptureCreateSQL(o *OrderInfo, tx *gorm.DB) {
	if !c.validator.IsEnabled() || c.db == nil {
		return
	}

	db := c.db
	if tx != nil {
		db = tx
	}

	// 清空之前的捕获
	c.mu.Lock()
	c.capturedSQL = ""
	c.capturedArgs = nil
	c.mu.Unlock()

	// 执行Create操作（会触发回调）
	// 使用DryRun模式避免实际插入数据
	result := db.Set("gorm:insert_option", "DRY_RUN").Create(o)

	// 获取捕获的SQL
	c.mu.RLock()
	sql := c.capturedSQL
	args := c.capturedArgs
	c.mu.RUnlock()

	if sql != "" {
		c.validator.CaptureSQL(c.method, "v1", sql, args, result.Error)
	}
}

// buildCreateSQLFromScope 从GORM scope构建CREATE SQL
func (c *GormV1SQLCapturer) buildCreateSQLFromScope(scope *gorm.Scope) string {
	if scope.SQL != "" {
		return scope.SQL
	}

	// 如果scope.SQL为空，手动构建
	tableName := scope.TableName()
	fields := scope.Fields()

	columns := make([]string, 0)
	placeholders := make([]string, 0)

	for _, field := range fields {
		if !field.IsPrimaryKey || !field.IsBlank {
			columns = append(columns, field.DBName)
			placeholders = append(placeholders, "?")
		}
	}

	return fmt.Sprintf("INSERT INTO %s (%s) VALUES (%s)",
		tableName,
		strings.Join(columns, ", "),
		strings.Join(placeholders, ", "))
}

// extractArgsFromScope 从GORM scope提取参数
func (c *GormV1SQLCapturer) extractArgsFromScope(scope *gorm.Scope) []interface{} {
	if len(scope.SQLVars) > 0 {
		return scope.SQLVars
	}

	// 如果SQLVars为空，从字段中提取
	args := make([]interface{}, 0)
	fields := scope.Fields()

	for _, field := range fields {
		if !field.IsPrimaryKey || !field.IsBlank {
			args = append(args, field.Field.Interface())
		}
	}

	return args
}

// CaptureQuerySQL 捕获查询操作的SQL
func (c *GormV1SQLCapturer) CaptureQuerySQL(o *OrderInfo, conditions map[string]interface{}) {
	if !c.validator.IsEnabled() || c.db == nil {
		return
	}

	// 清空之前的捕获
	c.mu.Lock()
	c.capturedSQL = ""
	c.capturedArgs = nil
	c.mu.Unlock()

	// 构建查询条件
	query := c.db.Model(o)

	if orderCode, ok := conditions["order_code"]; ok && orderCode != "" {
		query = query.Where("order_code = ?", orderCode)
	}

	if userID, ok := conditions["user_id"]; ok && userID.(uint) > 0 {
		query = query.Where("user_id = ?", userID)
	}

	// 执行查询（DryRun模式）
	var result OrderInfo
	query.Set("gorm:query_option", "DRY_RUN").First(&result)

	// 获取捕获的SQL
	c.mu.RLock()
	sql := c.capturedSQL
	args := c.capturedArgs
	c.mu.RUnlock()

	if sql != "" {
		c.validator.CaptureSQL(c.method, "v1", sql, args, nil)
	}
}

// CaptureUpdateSQL 捕获更新操作的SQL
func (c *GormV1SQLCapturer) CaptureUpdateSQL(o *OrderInfo, conditions map[string]interface{}, updates map[string]interface{}) {
	if !c.validator.IsEnabled() || c.db == nil {
		return
	}

	// 清空之前的捕获
	c.mu.Lock()
	c.capturedSQL = ""
	c.capturedArgs = nil
	c.mu.Unlock()

	// 构建更新查询
	query := c.db.Model(o)

	// 添加WHERE条件
	if id, ok := conditions["id"]; ok && id.(uint) > 0 {
		query = query.Where("id = ?", id)
	}

	if orderCode, ok := conditions["order_code"]; ok && orderCode != "" {
		query = query.Where("order_code = ?", orderCode)
	}

	if userID, ok := conditions["user_id"]; ok && userID.(uint) > 0 {
		query = query.Where("user_id = ?", userID)
	}

	// 执行更新（DryRun模式）
	query.Set("gorm:update_option", "DRY_RUN").Updates(updates)

	// 获取捕获的SQL
	c.mu.RLock()
	sql := c.capturedSQL
	args := c.capturedArgs
	c.mu.RUnlock()

	if sql != "" {
		c.validator.CaptureSQL(c.method, "v1", sql, args, nil)
	}
}

// CaptureDeleteSQL 捕获删除操作的SQL
func (c *GormV1SQLCapturer) CaptureDeleteSQL(o *OrderInfo, ids []uint) {
	if !c.validator.IsEnabled() || c.db == nil {
		return
	}

	// 清空之前的捕获
	c.mu.Lock()
	c.capturedSQL = ""
	c.capturedArgs = nil
	c.mu.Unlock()

	// 执行删除（DryRun模式）
	c.db.Set("gorm:delete_option", "DRY_RUN").Where("id IN (?)", ids).Delete(o)

	// 获取捕获的SQL
	c.mu.RLock()
	sql := c.capturedSQL
	args := c.capturedArgs
	c.mu.RUnlock()

	if sql != "" {
		c.validator.CaptureSQL(c.method, "v1", sql, args, nil)
	}
}

// GetCapturedSQL 获取最后捕获的SQL（用于测试）
func (c *GormV1SQLCapturer) GetCapturedSQL() (string, []interface{}) {
	c.mu.RLock()
	defer c.mu.RUnlock()
	return c.capturedSQL, c.capturedArgs
}

// ClearCaptured 清空捕获的SQL
func (c *GormV1SQLCapturer) ClearCaptured() {
	c.mu.Lock()
	defer c.mu.Unlock()
	c.capturedSQL = ""
	c.capturedArgs = nil
}


