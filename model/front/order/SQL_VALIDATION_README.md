# OrderInfo GORM v1 到 v2 SQL 验证功能

## 概述

本功能实现了 GORM v1 到 v2 迁移过程中的 SQL 语句对比验证，确保迁移不会导致业务逻辑偏差。

## 主要解决的问题

### 1. 参数处理差异
- **GORM v1**: 使用占位符 `?`
- **GORM v2**: 直接嵌入参数值

**示例**:
```sql
-- GORM v1
SELECT * FROM order_info WHERE order_code = ? AND user_id = ?

-- GORM v2  
SELECT * FROM order_info WHERE order_code = 'TEST001' AND user_id = 12345
```

### 2. 查询优化差异
- **GORM v2** 自动添加 `ORDER BY` 和 `LIMIT 1`
- **GORM v2** 的 `Updates().First()` 生成额外的 SELECT 查询

**示例**:
```sql
-- GORM v1
SELECT * FROM order_info WHERE order_code = ?

-- GORM v2
SELECT * FROM order_info WHERE order_code = 'TEST001' ORDER BY id LIMIT 1
```

### 3. 关联查询差异
- **GORM v1**: 使用 JOIN 语句进行关联查询
- **GORM v2**: 使用分离的查询

**示例**:
```sql
-- GORM v1
SELECT order_info.*, order_item.sku_id FROM order_info 
LEFT JOIN order_item ON order_info.order_code = order_item.order_code 
WHERE order_info.order_code = ?

-- GORM v2
SELECT * FROM order_info WHERE order_code = 'TEST001'
```

### 4. 软删除处理差异
- **GORM v1**: `deleted_at IS NULL`
- **GORM v2**: `deleted_at = 0`

## 核心组件

### 1. SQLValidator (`sql_validator.go`)
- **功能**: 核心 SQL 验证器
- **主要方法**:
  - `CaptureSQL()`: 捕获 SQL 语句
  - `CompareAndValidate()`: 对比并验证 SQL 差异
  - `isSQLEquivalent()`: 判断 SQL 等价性

### 2. GormV1SQLCapturer (`gorm_v1_capturer.go`)
- **功能**: GORM v1 SQL 捕获器
- **主要方法**:
  - `CaptureCreateSQL()`: 捕获 CREATE 操作 SQL
  - `CaptureQuerySQL()`: 捕获查询操作 SQL
  - `CaptureUpdateSQL()`: 捕获更新操作 SQL

### 3. SQLValidationConfig (`sql_validation_config.go`)
- **功能**: 配置管理
- **特性**:
  - 环境变量驱动
  - 方法级别的验证开关
  - 报警阈值控制

### 4. SQLCaptureLogger
- **功能**: GORM v2 SQL 捕获日志器
- **实现**: `gorm.io/gorm/logger.Interface`

## 使用方法

### 1. 环境变量配置
```bash
# 启用 SQL 验证
export SQL_VALIDATION_ENABLED=true
export ENVIRONMENT=development
export SQL_VALIDATION_LOG_DIFFERENCES=true
export SQL_VALIDATION_ALERT_THRESHOLD=5

# 指定启用验证的方法
export SQL_VALIDATION_ENABLED_METHODS="Create,Update,QueryByOrderCode"
```

### 2. 代码中使用
```go
// 自动启用（在 init() 中已初始化）
orderInfo := &OrderInfo{
    OrderCode: "TEST001",
    UserID:    12345,
}

// 执行操作时自动进行 SQL 验证
err := orderInfo.Create()
err = orderInfo.QueryByOrderCode()
err = orderInfo.Update()
```

### 3. 手动控制
```go
// 获取管理器
manager := GetSQLValidationManager()

// 启用/禁用验证
manager.StartValidation()
manager.StopValidation()

// 启用特定方法
manager.EnableMethodValidation("Create")
manager.DisableMethodValidation("Update")

// 获取验证报告
report := manager.GetValidationReport()
```

## 已迁移的方法

### 高优先级方法（核心业务）
- ✅ `Create()` - 订单创建
- ✅ `QueryByOrderCode()` - 根据订单号查询
- ✅ `GetOrderInfoByOrderCode()` - 获取订单详情
- ✅ `Update()` - 订单更新

### 验证功能
- ✅ 参数化查询对比
- ✅ 查询优化移除（ORDER BY, LIMIT）
- ✅ 关联查询处理
- ✅ 软删除条件标准化
- ✅ Updates().First() 模式识别

## 测试覆盖

### 1. 基础功能测试
- `TestSQLComparison` - 基本 SQL 对比
- `TestParameterReplacement` - 参数替换
- `TestAssociationQueryRemoval` - 关联查询移除

### 2. 高级功能测试
- `TestAdvancedSQLComparison` - 高级 SQL 对比
- `TestGormV2OptimizationRemoval` - GORM v2 优化移除
- `TestRealWorldSQLComparison` - 真实场景测试

### 3. 配置和接口测试
- `TestSQLValidatorInterface` - 验证器接口
- `TestConfigInterface` - 配置接口
- `TestUpdateFirstPattern` - Updates().First() 模式

## 安全保障

### 1. 环境隔离
- **生产环境**: 自动禁用验证
- **开发/测试环境**: 可配置启用

### 2. 性能保护
- 异步处理验证逻辑
- 最小化对业务性能的影响

### 3. 向后兼容
- 不影响现有业务逻辑
- 验证失败不会阻断业务

### 4. 可观测性
- 详细的差异日志
- 可配置的报警阈值
- 实时验证报告

## 监控和报警

### 1. SQL 差异检测
- 自动对比 v1 和 v2 生成的 SQL
- 智能识别等价的 SQL 语句
- 准确检测真正的差异

### 2. 阈值报警
- 达到配置的差异次数后自动报警
- 支持集成钉钉、邮件等报警系统

### 3. 详细日志
- 记录所有 SQL 差异的详细信息
- 包含原始 SQL、标准化 SQL、参数等

## 下一步计划

1. **扩展更多方法**: 为其他重要方法添加 SQL 验证
2. **集成报警系统**: 将报警集成到钉钉、邮件等系统
3. **性能监控**: 监控验证对性能的影响
4. **自动化测试**: 集成到 CI/CD 流程中

## 总结

这个 SQL 验证功能为 GORM v1 到 v2 的迁移提供了强有力的保障，能够：

- ✅ **准确识别** GORM 版本间的 SQL 差异
- ✅ **智能对比** 等价但形式不同的 SQL 语句
- ✅ **实时监控** 迁移过程中的潜在问题
- ✅ **保障业务** 逻辑的一致性和正确性

通过这个功能，可以安全、可靠地完成 OrderInfo 从 GORM v1 到 v2 的迁移，避免因 SQL 差异导致的业务逻辑偏差。
