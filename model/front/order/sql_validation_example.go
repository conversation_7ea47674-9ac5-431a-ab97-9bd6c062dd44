package order

import (
	"fmt"
	"os"
	"time"
	"zx/zx-consistency/pkg/define"
	"zx/zxgo/log"
)

// SQLValidationExample SQL验证使用示例
func SQLValidationExample() {
	fmt.Println("=== OrderInfo GORM v1 到 v2 SQL验证示例 ===")
	
	// 1. 初始化SQL验证
	manager := GetSQLValidationManager()
	manager.StartValidation()
	defer manager.StopValidation()
	
	// 2. 配置验证参数
	config := GetSQLValidationConfig()
	config.SetAlertThreshold(3) // 3次差异后报警
	
	// 3. 启用特定方法的验证
	config.EnableMethod("Create")
	config.EnableMethod("QueryByOrderCode")
	config.EnableMethod("Update")
	
	fmt.Printf("SQL验证配置: %+v\n", manager.GetValidationReport())
	
	// 4. 执行业务操作，自动进行SQL验证
	demonstrateCreateValidation()
	demonstrateQueryValidation()
	demonstrateUpdateValidation()
	
	// 5. 获取验证报告
	report := manager.GetValidationReport()
	fmt.Printf("最终验证报告: %+v\n", report)
}

// demonstrateCreateValidation 演示Create操作的SQL验证
func demonstrateCreateValidation() {
	fmt.Println("\n--- 演示Create操作SQL验证 ---")
	
	orderInfo := &OrderInfo{
		OrderCode:   fmt.Sprintf("DEMO-CREATE-%d", time.Now().Unix()),
		UserID:      12345,
		OrderState:  define.NOT_PAY,
		TotalMoney:  2599,
		OrderType:   POD_ORDER,
		ShopType:    ORDER_SHOP_TYPE_SHOPIFY,
		CreatedAt:   time.Now(),
		UpdatedAt:   time.Now(),
	}
	
	fmt.Printf("创建订单: %s\n", orderInfo.OrderCode)
	
	err := orderInfo.Create()
	if err != nil {
		fmt.Printf("创建失败: %v\n", err)
		return
	}
	
	fmt.Printf("创建成功，订单ID: %d\n", orderInfo.ID)
	
	// 查看捕获的SQL
	validator := GetSQLValidator()
	sqls := validator.GetCapturedSQLs("Create")
	for _, sql := range sqls {
		fmt.Printf("捕获的SQL (%s): %s\n", sql.Version, sql.SQL)
	}
}

// demonstrateQueryValidation 演示Query操作的SQL验证
func demonstrateQueryValidation() {
	fmt.Println("\n--- 演示Query操作SQL验证 ---")
	
	// 先创建一个测试订单
	testOrderCode := fmt.Sprintf("DEMO-QUERY-%d", time.Now().Unix())
	setupOrder := &OrderInfo{
		OrderCode:  testOrderCode,
		UserID:     12345,
		OrderState: define.NOT_PAY,
		TotalMoney: 1999,
		OrderType:  POD_ORDER,
		ShopType:   ORDER_SHOP_TYPE_SHOPIFY,
	}
	
	// 直接使用v2创建，避免SQL验证干扰
	err := setupOrder.createV2()
	if err != nil {
		fmt.Printf("设置测试订单失败: %v\n", err)
		return
	}
	
	fmt.Printf("查询订单: %s\n", testOrderCode)
	
	// 执行查询操作
	queryOrder := &OrderInfo{
		OrderCode: testOrderCode,
		UserID:    12345,
	}
	
	err = queryOrder.QueryByOrderCode()
	if err != nil {
		fmt.Printf("查询失败: %v\n", err)
		return
	}
	
	fmt.Printf("查询成功，订单状态: %d\n", queryOrder.OrderState)
	
	// 查看捕获的SQL
	validator := GetSQLValidator()
	sqls := validator.GetCapturedSQLs("QueryByOrderCode")
	for _, sql := range sqls {
		fmt.Printf("捕获的SQL (%s): %s\n", sql.Version, sql.SQL)
	}
}

// demonstrateUpdateValidation 演示Update操作的SQL验证
func demonstrateUpdateValidation() {
	fmt.Println("\n--- 演示Update操作SQL验证 ---")
	
	// 先创建一个测试订单
	testOrderCode := fmt.Sprintf("DEMO-UPDATE-%d", time.Now().Unix())
	setupOrder := &OrderInfo{
		OrderCode:  testOrderCode,
		UserID:     12345,
		OrderState: define.NOT_PAY,
		TotalMoney: 1999,
		OrderType:  POD_ORDER,
		ShopType:   ORDER_SHOP_TYPE_SHOPIFY,
	}
	
	err := setupOrder.createV2()
	if err != nil {
		fmt.Printf("设置测试订单失败: %v\n", err)
		return
	}
	
	fmt.Printf("更新订单: %s\n", testOrderCode)
	
	// 执行更新操作
	setupOrder.OrderState = define.HAVE_PAY
	setupOrder.TotalMoney = 2999
	setupOrder.PayTime = time.Now().Unix()
	
	err = setupOrder.Update()
	if err != nil {
		fmt.Printf("更新失败: %v\n", err)
		return
	}
	
	fmt.Printf("更新成功，新状态: %d\n", setupOrder.OrderState)
	
	// 查看捕获的SQL
	validator := GetSQLValidator()
	sqls := validator.GetCapturedSQLs("Update")
	for _, sql := range sqls {
		fmt.Printf("捕获的SQL (%s): %s\n", sql.Version, sql.SQL)
	}
}

// RunSQLValidationDemo 运行SQL验证演示
func RunSQLValidationDemo() {
	// 设置环境变量启用SQL验证
	os.Setenv("SQL_VALIDATION_ENABLED", "true")
	os.Setenv("ENVIRONMENT", "development")
	os.Setenv("SQL_VALIDATION_LOG_DIFFERENCES", "true")
	os.Setenv("SQL_VALIDATION_DETAILED_LOG", "true")
	
	SQLValidationExample()
}

// MonitorSQLDifferences 监控SQL差异的示例
func MonitorSQLDifferences() {
	fmt.Println("=== SQL差异监控示例 ===")
	
	validator := GetSQLValidator()
	
	// 模拟一些SQL差异
	validator.CaptureSQL("TestMethod", "v1", 
		"SELECT * FROM order_info WHERE order_code = ? AND deleted_at IS NULL", 
		[]interface{}{"TEST001"}, nil)
	
	validator.CaptureSQL("TestMethod", "v2", 
		"SELECT * FROM order_info WHERE order_code = ? AND deleted_at = 0", 
		[]interface{}{"TEST001"}, nil)
	
	// 检查差异
	if diff := validator.CompareAndValidate("TestMethod"); diff != nil {
		fmt.Printf("检测到SQL差异:\n")
		fmt.Printf("方法: %s\n", diff.Method)
		fmt.Printf("V1 SQL: %s\n", diff.V1SQL)
		fmt.Printf("V2 SQL: %s\n", diff.V2SQL)
		fmt.Printf("时间: %s\n", diff.Timestamp.Format("2006-01-02 15:04:05"))
	} else {
		fmt.Println("未检测到SQL差异")
	}
}

// ConfigureSQLValidation 配置SQL验证的示例
func ConfigureSQLValidation() {
	fmt.Println("=== SQL验证配置示例 ===")
	
	config := GetSQLValidationConfig()
	
	// 启用特定方法
	methods := []string{"Create", "Update", "QueryByOrderCode", "GetOrderInfoByOrderCode"}
	for _, method := range methods {
		config.EnableMethod(method)
		fmt.Printf("已启用方法: %s\n", method)
	}
	
	// 设置报警阈值
	config.SetAlertThreshold(5)
	fmt.Printf("设置报警阈值: %d\n", config.GetAlertThreshold())
	
	// 显示配置信息
	fmt.Printf("启用的方法: %v\n", config.GetEnabledMethods())
	fmt.Printf("是否记录差异: %v\n", config.ShouldLogDifferences())
	fmt.Printf("是否启用详细日志: %v\n", config.IsDetailedLoggingEnabled())
}

// ValidateSpecificMethod 验证特定方法的示例
func ValidateSpecificMethod(methodName string) {
	fmt.Printf("=== 验证方法 %s ===\n", methodName)
	
	config := GetSQLValidationConfig()
	if !config.IsMethodEnabled(methodName) {
		fmt.Printf("方法 %s 的SQL验证未启用\n", methodName)
		return
	}
	
	validator := GetSQLValidator()
	sqls := validator.GetCapturedSQLs(methodName)
	
	if len(sqls) == 0 {
		fmt.Printf("方法 %s 没有捕获到SQL\n", methodName)
		return
	}
	
	fmt.Printf("方法 %s 捕获到 %d 条SQL:\n", methodName, len(sqls))
	for i, sql := range sqls {
		fmt.Printf("%d. 版本: %s, SQL: %s\n", i+1, sql.Version, sql.SQL)
		if sql.Error != nil {
			fmt.Printf("   错误: %v\n", sql.Error)
		}
	}
	
	// 检查差异
	if diff := validator.CompareAndValidate(methodName); diff != nil {
		fmt.Printf("发现SQL差异!\n")
		log.Warn("SQL差异详情", "difference", diff)
	} else {
		fmt.Printf("SQL一致，无差异\n")
	}
}
